# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
DOCKER_README.md
*.md

# Docker files
Dockerfile
docker-compose*.yml
.dockerignore

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# Backup files
backups/
*.backup
*.bak

# Temporary files
tmp/
temp/
*.tmp

# IDE files
*.sublime-project
*.sublime-workspace

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
vendor/

# Database files (will be in volumes)
*.db
*.sqlite
*.sqlite3

# Static files that will be uploaded at runtime
static/pictures/*
!static/pictures/.gitkeep
