# Web Forum Project - image-upload

## Overview

A modern web forum application that enables users to communicate through posts and comments, with category-based organization and interactive features like likes/dislikes. The forum includes advanced filtering options, secure user authentication, and a responsive user interface with structured notifications. Built with **SQLite** for data persistence, **Docker** for containerization, and **Go** for the backend API. The frontend uses vanilla HTML, CSS, and JavaScript with a modular component architecture.

---

## Features

### 1. **User Authentication**

- Secure user registration with unique email, username, and password validation.
- Password encryption using **bcrypt** for enhanced security.
- Session management with secure cookies and expiration handling.
- Structured notification system for authentication feedback.
- Enter key support for login and signup forms.

### 2. **Posts and Comments**

- Only registered users can create posts and comments.
- Posts can be associated with one or more categories.
- Posts and comments are visible to all users (registered and non-registered).
- Non-registered users can only view posts and comments (no interaction).

### 3. **Likes and Dislikes**

- Only registered users can like or dislike posts and comments.
- The number of likes and dislikes is visible to all users.

### 4. **Filtering**

- Users can filter posts by:
  - Categories (e.g., show only posts in the "Technology" category).
  - Posts created by the logged-in user.
  - Posts liked by the logged-in user.
- Filtering by created and liked posts is only available for registered users.

### 5. **User Experience & Notifications**

- Modern notification system replacing browser alerts with styled UI components.
- Context-specific error messages with actionable guidance.
- Toast notifications for success, error, warning, and info messages.
- Confirmation dialogs and prompt modals for user interactions.
- Responsive design with mobile-friendly interface.

### 6. **Error Handling**

- Comprehensive error handling for website errors (404, 500) with appropriate HTTP status codes.
- Graceful handling of technical errors (database issues, invalid input).
- User-friendly error messages with clear next steps.

---

## Technical Requirements

### 1. **Database**

- **SQLite** is used to store data (users, posts, comments, categories, likes/dislikes).
- The database schema is designed efficiently, and an entity-relationship diagram (ERD) is provided.

### 2. **Backend**

- The backend is implemented in **Go**.
- RESTful API endpoints are provided for all functionalities (authentication, posts, comments, likes/dislikes, filtering).
- Middleware is used for authentication and logging.

### 3. **Frontend**

- Modern frontend built with vanilla HTML, CSS, and JavaScript using modular component architecture.
- Features include:
  - Responsive registration and login forms with Enter key support.
  - Interactive post creation and viewing with real-time feedback.
  - Comprehensive commenting system with threaded replies.
  - Like/dislike functionality with immediate visual feedback.
  - Advanced filtering options (categories, user posts, liked posts).
  - Structured notification system replacing browser alerts.
  - Mobile-responsive design with touch-friendly interface.

### 4. **Docker**

- The application is containerized using Docker.
- A `Dockerfile` and `docker-compose.yml` file are provided for easy setup and deployment.

### 5. **Security**

- Passwords are encrypted using **bcrypt**.
- Cookies with expiration dates are used for session management.
- Optionally, UUIDs are used for session IDs (bonus task).

### 6. **Testing**

- Unit tests are written for backend functionality (e.g., handlers, models, utilities).
- The application is tested to ensure it is free of critical bugs and handles edge cases gracefully.

---

## Directory Structure

```bash
forum/
├── backend/
│   ├── sqlite/               # SQLite database setup and queries
│   │   ├── database.go       # Database connection and initialization
│   │   ├── database_test.go  # Database connection and initialization test
│   │   ├── queries_test.go   # SQL queries test
│   │   └── queries.go        # SQL queries (CREATE, INSERT, SELECT, etc.)
│   ├── models/               # Data models (structs for users, posts, comments, etc.)
│   │   ├── user.go
│   │   ├── trends.go
│   │   ├── models_test.go
│   │   ├── post.go
│   │   ├── comment.go
│   │   └── category.go
│   ├── handlers/             # HTTP handlers (logic for handling requests)
│   │   ├── auth.go           # Authentication handlers (register, login, logout)
│   │   ├── auth_test.go      # Authentication handlers (register, login, logout)test
│   │   ├── post.go           # Post-related handlers (create, read, update, delete)
│   │   ├── categoty.go       # list of categoties
│   │   ├── comment.go        # Comment-related handlers
│   │   └── like.go           # Like/dislike handlers
│   ├── routes/               # API routes
│   │   └── routes.go         # Define all API endpoints
│   ├── middleware/           # Middleware (authentication, logging, etc.)
│   │   ├── auth.go           # Auth middleware (check if user is logged in)
│   │   └── cors.go           # cross-origin resource sharing
│   ├── main.go               # Entry point for the backend
│   └── utils/                # Utility functions (e.g., password hashing, UUID generation)
│       ├── auth_utils.go
│       ├── auth_utils_test.go
│       └── response_utils.go # Helper functions for JSON responses
│       └── response_utils_test.go 
├── frontend/
│   ├── index.html            # Main HTML file
│   ├── app.mjs               # Main JavaScript entry point
│   ├── components/           # Modular component architecture
│   │   ├── auth/             # Authentication components
│   │   │   ├── AuthManager.mjs
│   │   │   └── AuthModal.mjs
│   │   ├── posts/            # Post-related components
│   │   │   ├── PostManager.mjs
│   │   │   ├── PostForm.mjs
│   │   │   └── PostCard.mjs
│   │   ├── comments/         # Comment system
│   │   │   └── CommentManager.mjs
│   │   ├── notifications/    # Notification system
│   │   │   └── NotificationManager.mjs
│   │   ├── navigation/       # Navigation components
│   │   │   ├── NavManager.mjs
│   │   │   └── MobileNavManager.mjs
│   │   ├── reactions/        # Like/dislike functionality
│   │   │   └── ReactionManager.mjs
│   │   ├── categories/       # Category management
│   │   │   └── CategoryManager.mjs
│   │   ├── router/           # Client-side routing
│   │   │   └── Router.mjs
│   │   ├── views/            # Page views
│   │   │   ├── HomeView.mjs
│   │   │   ├── PostDetailView.mjs
│   │   │   ├── ProfileView.mjs
│   │   │   └── TrendingView.mjs
│   │   └── utils/            # Utility functions
│   │       ├── ApiUtils.mjs
│   │       ├── TimeUtils.mjs
│   │       └── ValidationUtils.mjs
│   ├── styles/               # CSS styling
│   │   └── styles.css
│   └── static/               # Static assets (images, icons, etc.)
├── Dockerfile                # Dockerfile for containerizing the backend
├── docker-compose.yml        # Docker Compose file for multi-container setup
├── docker-compose.serve.yml # Docker Compose file for multi-container setup during develop
├── Makefile                  # Excecutable commands for building and running the app using make tool
├── start-forum.sh            # Script for starting the containerised application
├── stop-forum.sh            # Script for stopping the containerised application
├── test-docker.sh            # Testing Docker containerization setup.
└── README.md                 # Project documentation
```

---

## Key Features

### Modern User Interface

- **Structured Notification System**: Custom toast notifications, modals, and alerts replace browser popups
- **Context-Specific Error Messages**: Clear, actionable feedback for different scenarios
- **Responsive Design**: Mobile-friendly interface with touch-optimized interactions
- **Enter Key Support**: Standard form submission behavior for login and signup

### Enhanced User Experience

- **Real-time Feedback**: Immediate visual responses to user actions
- **Progressive Disclosure**: Layered information presentation for better usability
- **Accessibility**: Keyboard navigation, focus management, and screen reader support
- **Intuitive Navigation**: Clean, organized interface with clear visual hierarchy

---

## Setup Instructions

### Prerequisites

- Docker installed on your machine.
- Go installed (if running locally without Docker).

### Steps

1. Clone the repository:

   ```bash
   git clone https://learn.zone01kisumu.ke/git/hshikuku/forum.git
   cd forum
   ```

2. Build and run the application using Docker:

   ```bash
   docker-compose up --build
   ```

3. Access the application:
   - Frontend: Open `http://localhost:8080` in your browser.
   - Backend API: Access endpoints at `http://localhost:8080/api`.
   - API Documentation: See `backend/README.md` for detailed endpoint documentation.

4. (Optional) Run the backend locally:
   - Navigate to the `backend` directory:

     ```bash
     cd backend
     ```

   - Run the Go application:

     ```bash
     go run main.go
     ```

---

## Testing

- Run unit tests for the backend:

  ```bash
  cd backend
  go test ./...
  ```

---

## Authors

- [Barrack Otieno](http://www.github.com/baraq23)
- [Hezborn Shikuku](http://www.github.com/Mania124)
- [Otieno Ragwel](http://www.github.com/Oragwel)
- [Moffat Mokwa](https://learn.zone01kisumu.ke/git/mmoffat)
- [Samuel Omulo](http://www.github.com/Somulo1)

---

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

## Acknowledgments

- Thanks to the Go community for excellent documentation and libraries.
- Inspiration from popular web forums like Reddit and Stack Overflow.
