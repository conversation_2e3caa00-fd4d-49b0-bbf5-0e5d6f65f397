# Forum Frontend Documentation - Image Upload

## Table of Contents

1. [Project Overview](#project-overview)
2. [Key Features](#key-features)
3. [Project Structure](#project-structure)
4. [Components](#components)
5. [Notification System](#notification-system)
6. [Authentication Flow](#authentication-flow)
7. [API Interactions](#api-interactions)
8. [Running the Frontend](#running-the-frontend)
9. [Development](#development)

## Project Overview

This is a modern, single-page web application for a forum platform with image upload capabilities. The frontend is built using vanilla JavaScript with a modular component architecture, providing a responsive, interactive user experience with structured notifications and enhanced UX features.

## Key Features

### Modern User Interface

- **Structured Notification System**: Custom toast notifications, modals, and alerts replace browser popups
- **Context-Specific Error Messages**: Clear, actionable feedback for different scenarios
- **Responsive Design**: Mobile-friendly interface with touch-optimized interactions
- **Enter Key Support**: Standard form submission behavior for login and signup

### Enhanced User Experience

- **Real-time Feedback**: Immediate visual responses to user actions
- **Progressive Disclosure**: Layered information presentation for better usability
- **Accessibility**: Keyboard navigation, focus management, and screen reader support
- **Intuitive Navigation**: Clean, organized interface with clear visual hierarchy

### Technical Features

- **Modular Architecture**: Component-based structure for maintainability
- **Client-side Routing**: Smooth navigation without page reloads
- **Image Upload Support**: Drag-and-drop file uploads with validation
- **Session Management**: Secure authentication with proper state handling

## Project Structure

```text
frontend/
├── index.html                # Main HTML file
├── app.mjs                   # Main JavaScript entry point
├── components/               # Modular component architecture
│   ├── auth/                 # Authentication components
│   │   ├── AuthManager.mjs   # User authentication logic
│   │   └── AuthModal.mjs     # Login/signup modal interface
│   ├── posts/                # Post-related components
│   │   ├── PostManager.mjs   # Post data management
│   │   ├── PostForm.mjs      # Post creation form
│   │   └── PostCard.mjs      # Individual post display
│   ├── comments/             # Comment system
│   │   └── CommentManager.mjs # Comment functionality
│   ├── notifications/        # Notification system
│   │   ├── NotificationManager.mjs # Toast and modal notifications
│   │   └── NotificationTester.mjs  # Testing utilities
│   ├── navigation/           # Navigation components
│   │   ├── NavManager.mjs    # Main navigation
│   │   └── MobileNavManager.mjs # Mobile navigation
│   ├── reactions/            # Like/dislike functionality
│   │   └── ReactionManager.mjs
│   ├── categories/           # Category management
│   │   └── CategoryManager.mjs
│   ├── router/               # Client-side routing
│   │   └── Router.mjs
│   ├── views/                # Page views
│   │   ├── HomeView.mjs      # Home page
│   │   ├── PostDetailView.mjs # Individual post view
│   │   ├── ProfileView.mjs   # User profile
│   │   └── TrendingView.mjs  # Trending content
│   ├── core/                 # Core application
│   │   └── App.mjs           # Main application controller
│   └── utils/                # Utility functions
│       ├── ApiUtils.mjs      # API communication
│       ├── TimeUtils.mjs     # Time formatting
│       └── ValidationUtils.mjs # Form validation
├── styles/                   # CSS styling
│   └── styles.css           # Main stylesheet
└── static/                  # Static assets
    └── uploads/             # User uploaded images
```

## Components

### Authentication System

#### AuthManager ([components/auth/AuthManager.mjs](./components/auth/AuthManager.mjs))

- Handles core authentication logic and session management
- Key methods:
  - `login(email, password)`: Authenticates user with server
  - `register(formData)`: Registers new user with avatar support
  - `logout()`: Clears session and user state
  - `getCurrentUser()`: Returns current authenticated user
  - `validateRegistrationData()`: Client-side form validation

#### AuthModal ([components/auth/AuthModal.mjs](./components/auth/AuthModal.mjs))

- Manages authentication UI and user interactions
- Features:
  - Modal-based login and signup forms
  - Enter key support for form submission
  - Structured notification feedback
  - Avatar upload with drag-and-drop support
  - Real-time form validation

### Post Management System

#### PostManager ([components/posts/PostManager.mjs](./components/posts/PostManager.mjs))

- Handles post data operations and state management
- Key functionalities:
  - `fetchPosts()`: Retrieves posts with filtering options
  - `createPost()`: Creates new posts with image upload
  - `updatePost()`: Modifies existing posts
  - `deletePost()`: Removes posts with confirmation

#### PostForm ([components/posts/PostForm.mjs](./components/posts/PostForm.mjs))

- Manages post creation interface
- Features:
  - Rich text content editing
  - Image upload with drag-and-drop
  - Category selection
  - Real-time validation with notifications

#### PostCard ([components/posts/PostCard.mjs](./components/posts/PostCard.mjs))

- Displays individual posts in feed
- Includes reaction buttons, sharing, and navigation to detail view

## Notification System

### NotificationManager ([components/notifications/NotificationManager.mjs](./components/notifications/NotificationManager.mjs))

The notification system replaces browser alerts with structured, accessible UI components:

#### Toast Notifications

- **Success**: Green notifications for successful actions
- **Error**: Red notifications for errors with specific guidance
- **Warning**: Orange notifications for validation issues
- **Info**: Blue notifications for general information

#### Modal Dialogs

- **Alert**: Replaces `alert()` with styled modal
- **Confirm**: Replaces `confirm()` with custom confirmation dialog
- **Prompt**: Replaces `prompt()` with styled input modal

#### Features

- **Context-specific messages**: Different messages for different scenarios
- **Progressive disclosure**: Main message followed by helpful suggestions
- **Accessibility**: Keyboard navigation and screen reader support
- **Mobile responsive**: Optimized for touch devices
- **Auto-dismiss**: Configurable timeout for toast notifications

#### Usage Examples

```javascript
// Show success notification
notificationManager.success('Post created successfully!');

// Show error with suggestion
notificationManager.error('Invalid credentials');
setTimeout(() => {
    notificationManager.info('Don\'t have an account? Sign up!');
}, 2000);

// Show confirmation dialog
const confirmed = await notificationManager.showConfirm(
    'Are you sure you want to delete this post?',
    'Confirm Deletion'
);
```

### Category Management

#### CategoryManager ([components/categories/CategoryManager.mjs](./components/categories/CategoryManager.mjs))

- Handles category-related operations and filtering
- Responsibilities:
  - `fetchCategories()`: Retrieves available categories
  - `filterPostsByCategory()`: Filters posts by selected category
  - `createCategory()`: Creates new categories

### Other Core Components

#### CommentManager ([components/comments/CommentManager.mjs](./components/comments/CommentManager.mjs))

- Handles comment creation, display, and threading
- Supports nested replies and real-time updates

#### ReactionManager ([components/reactions/ReactionManager.mjs](./components/reactions/ReactionManager.mjs))

- Manages like/dislike functionality for posts and comments
- Provides immediate visual feedback

#### Router ([components/router/Router.mjs](./components/router/Router.mjs))

- Client-side routing for single-page application behavior
- Handles navigation without page reloads

#### NavManager ([components/navigation/NavManager.mjs](./components/navigation/NavManager.mjs))

- Manages main navigation and user authentication state
- Responsive design with mobile navigation support

## Authentication Flow

The application uses a secure, session-based authentication system:

1. **User Registration/Login**:
   - Users interact with the AuthModal component
   - Forms include Enter key support for better UX
   - Real-time validation with structured notifications

2. **Session Management**:
   - Cookie-based authentication with secure session handling
   - Automatic session verification on page load
   - Graceful handling of expired sessions

3. **User Feedback**:
   - Context-specific error messages for different scenarios
   - Success notifications for completed actions
   - Progressive disclosure of helpful information

## API Interactions

The frontend communicates with the backend through the ApiUtils module, which provides:

### Core Features

- **Centralized API communication**: Single point for all HTTP requests
- **Error handling**: Context-specific error messages with user guidance
- **Authentication**: Automatic session management
- **File uploads**: Support for image uploads with validation

### Key API Endpoints Used

- **Authentication**: `/api/login`, `/api/register`, `/api/logout`
- **Posts**: `/api/posts`, `/api/posts/create` (with image upload)
- **Comments**: `/api/comments/create`, `/api/comments/get`
- **Reactions**: `/api/likes/toggle`, `/api/likes/reactions`
- **Categories**: `/api/categories`
- **Files**: `/api/files/{filename}` for image serving

### Error Handling

The ApiUtils module provides enhanced error handling with:

- HTTP status code interpretation (401, 403, 404, 500, etc.)
- Context-specific error messages
- Integration with the notification system
- Automatic retry suggestions for network issues

For detailed API documentation, see [backend/README.md](../backend/README.md).

## Running the Frontend

To run the frontend of the forum application, ensure you are in the `frontend` directory. You can use one of the following methods:

1. **Using npx serve (Recommended)**:
   - First, ensure you have Node.js and npm installed on your machine.
   - Navigate to the `frontend` directory:

     ```bash
     cd /path/to/your/forum/frontend
     ```

   - Run the server using npx serve:

     ```bash
     npx serve -s . -l 8000
     ```

   - **Access the Application**: Open your web browser and go to `http://localhost:8000`.

2. **Using npm with a Development Script**:
   - First, ensure you have Node.js and npm installed on your machine.
   - Navigate to the `frontend` directory:

     ```bash
     cd /path/to/your/forum/frontend
     ```

   - Install the necessary npm packages defined in your `package.json`:

     ```bash
     npm install
     ```

   - If you have a development script defined in your `package.json`, you can start the development server by running:

     ```bash
     npm run dev
     ```

   - **Access the Application**: Open your web browser and go to the URL specified in your script (usually `http://localhost:8000` or similar).

## Development

### Code Organization

- **Modular Architecture**: Each component is self-contained with clear responsibilities
- **ES6 Modules**: Modern JavaScript module system for better organization
- **Separation of Concerns**: UI, logic, and data handling are properly separated

### Development Tools

- **Browser DevTools**: Use for debugging and performance analysis
- **Console Testing**: Notification system includes testing utilities
- **Live Reload**: Use tools like `live-server` for automatic page refresh during development

### Best Practices

- **Component Isolation**: Each component manages its own state and dependencies
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Accessibility**: Keyboard navigation and screen reader support
- **Performance**: Efficient DOM manipulation and event handling

## Deployment

Deployment involves making your application available to users on the internet. Here are the recommended steps:

1. **Build the Application**:
   - Ensure all assets are optimized for production. Use tools like Webpack or Parcel to bundle your JavaScript files, minify CSS, and optimize images. This reduces load times and improves performance.
   - Example command to build with Webpack:

     ```bash
     npx webpack --mode production
     ```

2. **Choose a Hosting Platform**:
   - Select a platform that suits your needs:
     - **Netlify**: Ideal for static sites with continuous deployment from Git repositories.
     - **Vercel**: Great for serverless applications and frontend frameworks.
     - **GitHub Pages**: Simple hosting for static sites directly from a GitHub repository.
     - **Firebase Hosting**: Fast and secure hosting for web apps with a simple deployment process.

3. **Configure Environment Variables**:
   - Set up any necessary environment variables for API endpoints or other configurations. This is especially important for sensitive information like API keys. Most hosting platforms provide a way to set environment variables through their dashboard or CLI.

4. **Deploy**:
   - Follow the hosting platform's instructions to deploy your application. This often involves pushing your code to a Git repository or using CLI tools provided by the platform.
   - Example commands for deploying to Netlify and Vercel:
     - **Netlify**:

       ```bash
       netlify deploy --prod
       ```

     - **Vercel**:

       ```bash
       vercel --prod
       ```

5. **Monitor and Maintain**:
   - After deployment, monitor the application for performance and errors. Use tools like Google Analytics for tracking user interactions and performance metrics.
   - Set up error logging to catch any issues that users may encounter. Services like Sentry or LogRocket can help with this.
   - Be prepared to make updates as needed, whether for bug fixes, performance improvements, or new features.

6. **Documentation and Support**:
   - Ensure that your application is well-documented, both for users and for future developers. This includes a clear README, inline code comments, and any necessary user guides.
   - Consider setting up a support channel (like a Discord server or a dedicated email) for users to report issues or ask questions.
