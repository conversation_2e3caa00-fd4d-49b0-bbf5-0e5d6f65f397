
<!DOCTYPE html>
<html lang="en">
<head>
<<<<<<< HEAD
<<<<<<< HEAD
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Forum</title>
<<<<<<< HEAD
<<<<<<< HEAD
    <link rel="icon" href="./chat.png">
    <link rel="stylesheet" href="/styles.css" />
=======
    <link rel="icon" href="/static/pictures/forum-logo.png" type="image/x-icon"/>
    <link rel="stylesheet" href="/styles/styles.css" />
>>>>>>> 1b81024 (Fix Docker containerization issues)
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Add blur effect styles */
        .main-container {
            transition: filter 0.3s ease-in-out;
        }
        
        .main-container.blur {
            filter: blur(5px) brightness(0.7);
            pointer-events: none;
        }
    </style>
</head>
<body>
<<<<<<< HEAD
    <!-- Navbar -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="nav-brand" id="navBrand">Forum</div>
        <div class="nav-search" role="search">
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="searchInput" placeholder="Search..." aria-label="Search forum" />
        </div>
        <div class="nav-auth" id="navAuth" role="group" aria-label="User Authentication">
=======
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
=======
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
>>>>>>> ef11096 (Key Improvements Confirmed)
    <title>Forum</title>
=======
    <link rel="icon" href="http://localhost:8080/static/pictures/forum-logo.png" type="image/x-icon"/>
<<<<<<< HEAD
>>>>>>> 0649d9b (db changes)
    <link rel="stylesheet" href="styles/styles.css" />
=======
    <link rel="stylesheet" href="/styles/styles.css" />
>>>>>>> 380797e (fix: Use absolute paths for CSS and JS to prevent loading issues on nested routes)
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Add blur effect styles */
        .main-container {
            transition: filter 0.3s ease-in-out;
        }
        
        .main-container.blur {
            filter: blur(5px) brightness(0.7);
            pointer-events: none;
        }
    </style>
</head>
<body>
<<<<<<< HEAD
    <!-- Navbar -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div id="navLogoContainer"></div> <!-- Placeholder for dynamic logo -->
        <!-- <div class="nav-brand" id="navBrand">Forum</div> -->
        <div class="nav-search" role="search">
            <i class="fas fa-search search-icon"></i>
            <input type="text" id="searchInput" placeholder="Search..." aria-label="Search forum" />
        </div>
<<<<<<< HEAD
        <div id="navAuth" class="nav-auth">
>>>>>>> cb8bffe (add frontend sample)
=======
        <div class="nav-auth" id="navAuth" role="group" aria-label="User Authentication">
>>>>>>> ef11096 (Key Improvements Confirmed)
            <!-- Auth buttons will be inserted here -->
        </div>
    </nav>
    

<<<<<<< HEAD
<<<<<<< HEAD
    <!-- Main Layout -->
    <div class="container">
        <!-- Left Sidebar -->
        <aside class="sidebar left-sidebar" aria-label="Sidebar navigation">
            <div class="profile-section" id="userProfile">
                <!-- User profile will load dynamically -->
=======
    <div class="main-container">
        <!-- Mobile Navigation Elements -->
        <div class="hamburger-menu" aria-label="Toggle sidebar menu">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="mobile-category-dropdown">
            <button class="category-dropdown-btn" aria-label="Category filter">
                <i class="fas fa-tags"></i>
                <span>Categories</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="category-dropdown-content">
                <div class="category-item active" data-category="all">
                    <i class="fas fa-globe"></i> All Posts
                </div>
                <!-- Additional categories will be loaded dynamically -->
            </div>
        </div>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay"></div>

        <!-- Navbar -->
        <nav class="navbar" role="navigation" aria-label="Main navigation">
            <div id="navLogoContainer"></div>
<<<<<<< HEAD
            <div class="nav-search" role="search">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" placeholder="Search..." aria-label="Search forum" />
>>>>>>> 59cade2 (feat: add form for authentication)
            </div>
=======
>>>>>>> 971e353 (feat: Remove search functionality from navbar)
            <div class="nav-auth" id="navAuth" role="group" aria-label="User Authentication">

                <!-- Avatar, Username & Logout Button will appear here -->
            </div>
        </nav>
        
        

<<<<<<< HEAD
        <!-- Main Content -->
        <main class="main-content" id="mainContent" role="main">
            <section id="storySection" class="story-section">
                <!-- Stories will be rendered dynamically -->
            </section>
            <section id="createPostSection" class="create-post-section">
            </section>
            
            <section id="postFeed">
                <!-- Posts will be injected here -->
                 
            </section>
        </main>
=======
        <!-- Main Layout -->
        <div class="container">
            <!-- Left Sidebar -->
            <aside class="sidebar left-sidebar" aria-label="Sidebar navigation">
                <div class="profile-section" id="userProfile">
                    <!-- User profile will load dynamically -->
                </div>
                <nav class="menu-section" aria-label="Sidebar menu">
                    <div class="menu-item active" data-view="home"><i class="fas fa-home"></i> Home</div>
                    <div class="menu-item" data-view="profile"><i class="fas fa-user"></i> Profile</div>
                    <div class="menu-item" data-view="trending"><i class="fas fa-fire"></i> Trending</div>
                    <div class="menu-item" data-view="myposts"><i class="fas fa-folder"></i> My Posts</div>
                    <div class="menu-item" data-view="likedposts"><i class="fas fa-heart"></i> Liked Posts</div>
                </nav>
            </aside>
>>>>>>> 59cade2 (feat: add form for authentication)

            <!-- Main Content -->
            <main class="main-content" id="mainContent" role="main">
                <section id="storySection" class="story-section">
                    <!-- Stories will be rendered dynamically -->
                </section>
                <section id="createPostSection" class="create-post-section">
                </section>
                
                <section id="postFeed">
                    <!-- Posts will be injected here -->
                </section>
            </main>

            <!-- Right Sidebar -->
            <aside class="sidebar right-sidebar" aria-label="Right sidebar">
                <section id="categoryFilter" class="category-section">
                    <h3>Categories</h3>
                    <!-- Dynamic category filters -->
                </section>
            </aside>
        </div>
    </div>

    <!-- Modals -->
    <!-- Auth Modal -->
    <div id="authModal" class="modal hidden" role="dialog" aria-modal="true">
        <div class="auth-modal-overlay">
            <div class="auth-modal-container">
                <button class="close-btn" aria-label="Close">&times;</button>

                <!-- Tab Navigation -->
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="signin">Sign In</button>
                    <button class="auth-tab" data-tab="signup">Sign Up</button>
                </div>

                <!-- Sign In Form -->
                <div class="auth-form-container signin-form active">
                    <div class="auth-form">
                        <div class="form-header">
                            <h2>Welcome Back</h2>
                            <p>Sign in to your account to continue</p>
                        </div>

                        <form class="form-content">
                            <div class="form-group">
                                <label for="signin-email">Email Address</label>
                                <input type="email" id="signin-email" name="email" required />
                            </div>

                            <div class="form-group">
                                <label for="signin-password">Password</label>
                                <input type="password" id="signin-password" name="password" required />
                            </div>

                            <button type="button" class="submit-btn signin-submit">Sign In</button>
                        </form>

                        <div class="form-footer">
                            <p>Don't have an account? <span class="toggle-signup">Sign up here</span></p>
                        </div>
                    </div>
                </div>

                <!-- Sign Up Form -->
                <div class="auth-form-container signup-form">
                    <div class="auth-form">
                        <div class="form-header">
                            <h2>Create Account</h2>
                            <p>Join our community today</p>
                        </div>

                        <form class="form-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="signup-username">Username</label>
                                    <input type="text" id="signup-username" name="username" required />
                                </div>

                                <div class="form-group">
                                    <label for="signup-email">Email Address</label>
                                    <input type="email" id="signup-email" name="email" required />
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="signup-avatar">Profile Picture (Optional)</label>
                                <div class="file-input-wrapper">
                                    <input type="file" id="signup-avatar" name="avatar" accept="image/*" />
                                    <span class="file-input-text">Choose file or drag here</span>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="signup-password">Password</label>
                                    <input type="password" id="signup-password" name="password" required />
                                </div>

                                <div class="form-group">
                                    <label for="signup-confirm">Confirm Password</label>
                                    <input type="password" id="signup-confirm" name="confirmPassword" required />
                                </div>
                            </div>

                            <button type="button" class="submit-btn signup-submit">Create Account</button>
                        </form>

                        <div class="form-footer">
                            <p>Already have an account? <span class="toggle-signin">Sign in here</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  
        <!-- Create Post Modal -->
        <div id="createPostModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close post modal">&times;</span>
                <div id="postForm"></div>
            </div>
        </div>

        <!-- Profile Modal -->
        <div id="profileModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close profile modal">&times;</span>
                <div id="profileForm"></div>
            </div>
=======
=======
    <!-- Main Layout -->
>>>>>>> ef11096 (Key Improvements Confirmed)
    <div class="container">
        <!-- Left Sidebar -->
        <aside class="sidebar left-sidebar" aria-label="Sidebar navigation">
            <div class="profile-section" id="userProfile">
                <!-- User profile will load dynamically -->
=======
    <div class="main-container">
        <!-- Navbar -->
        <nav class="navbar" role="navigation" aria-label="Main navigation">
            <div id="navLogoContainer"></div>
            <div class="nav-search" role="search">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" placeholder="Search..." aria-label="Search forum" />
>>>>>>> fa3cded (feat: add form for authentication)
            </div>
            <div class="nav-auth" id="navAuth" role="group" aria-label="User Authentication">
                
                <!-- Avatar, Username & Logout Button will appear here -->
            </div>
        </nav>
        
        

<<<<<<< HEAD
        <!-- Main Content -->
        <main class="main-content" id="mainContent" role="main">
            <section id="storySection" class="story-section">
                <!-- Stories will be rendered dynamically -->
            </section>
            <section id="createPostSection" class="create-post-section">
                <!-- Create post form will be rendered dynamically -->
            </section>
            <section id="postFeed">
                <!-- Posts will be injected here -->
            </section>
        </main>
=======
        <!-- Main Layout -->
        <div class="container">
            <!-- Left Sidebar -->
            <aside class="sidebar left-sidebar" aria-label="Sidebar navigation">
                <div class="profile-section" id="userProfile">
                    <!-- User profile will load dynamically -->
                </div>
                <nav class="menu-section" aria-label="Sidebar menu">
                    <div class="menu-item active" data-view="home"><i class="fas fa-home"></i> Home</div>
                    <div class="menu-item" data-view="profile"><i class="fas fa-user"></i> Profile</div>
                    <div class="menu-item" data-view="trending"><i class="fas fa-fire"></i> Trending</div>
                    <div class="menu-item" data-view="saved"><i class="fas fa-bookmark"></i> Saved</div>
                </nav>
            </aside>
>>>>>>> fa3cded (feat: add form for authentication)

            <!-- Main Content -->
            <main class="main-content" id="mainContent" role="main">
                <section id="storySection" class="story-section">
                    <!-- Stories will be rendered dynamically -->
                </section>
                <section id="createPostSection" class="create-post-section">
                </section>
                
                <section id="postFeed">
                    <!-- Posts will be injected here -->
                </section>
            </main>

            <!-- Right Sidebar -->
            <aside class="sidebar right-sidebar" aria-label="Right sidebar">
                <section id="categoryFilter" class="category-section">
                    <h3>Categories</h3>
                    <!-- Dynamic category filters -->
                </section>
                <section id="trendingSection" class="trending-section">
                    <h3>Trending</h3>
                    <!-- Trending topics dynamically -->
                </section>
                <section id="suggestedUsers" class="suggested-section">
                    <h3>Suggested Users</h3>
                    <!-- Suggested users dynamically -->
                </section>
            </aside>
        </div>
    </div>

    <!-- Modals -->
   <!-- Auth Modal -->
<div id="authModal" class="modal hidden" role="dialog" aria-modal="true">
    <div class="cont">
        <button class="close-btn" aria-label="Close">&times;</button>
        
        <!-- Left side - Sign In -->
        <div class="form sign-in">
            <h2>Welcome Back</h2>
            <label>
                <input type="email" name="email" placeholder=" " required />
                <span>Email</span>
            </label>
            <label>
                <input type="password" name="password" placeholder=" " required />
                <span>Password</span>
            </label>
            <button type="button" class="submit">Sign In</button>
        </div>

        <!-- Right side - Sign Up -->
        <div class="sub-cont">
            <!-- Sliding overlay -->
            <div class="img">
                <div class="img-text m-in">
                    <h2>One of us?</h2>
                    <p>If you don't have an account, just sign up.</p>
                </div>
                <!-- Text for Sign In state -->
                <div class="img-text m-up">
                    <h2>One of us?</h2>
                    <p>If you already have an account, just sign in.</p>
                </div>
                <!-- Toggle button -->
                <div class="img-btn">
                    <span class="m-up">Sign In</span>
                    <span class="m-in">Sign Up</span>
                </div>
            </div>

            <!-- Sign Up Form -->
            <div class="form sign-up">
                <h2>Create Account</h2>
                <label>
                    <input type="text" name="username" placeholder=" " required />
                    <span>Username</span>
                </label>
                <label>
                    <input type="email" name="email" placeholder=" " required />
                    <span>Email</span>
                </label>
                <label>
                    <input type="file" name="avatar" id="avatar" accept="image/*" />
                    <span>Profile Picture</span>
                </label>
                <label>
                    <input type="password" name="password" placeholder=" " required />
                    <span>Password</span>
                </label>
                <label>
                    <input type="password" name="confirmPassword" placeholder=" " required />
                    <span>Confirm Password</span>
                </label>
                <button type="button" class="submit">Sign Up</button>
            </div>
        </div>
    </div>
</div>
  
        <!-- Create Post Modal -->
        <div id="createPostModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close post modal">&times;</span>
                <div id="postForm"></div>
            </div>
        </div>

<<<<<<< HEAD
    <!-- Profile Modal -->
    <div id="profileModal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="profileForm"></div>
>>>>>>> cb8bffe (add frontend sample)
=======
        <!-- Profile Modal -->
        <div id="profileModal" class="modal hidden" role="dialog" aria-modal="true">
            <div class="modal-content">
                <span class="close" role="button" aria-label="Close profile modal">&times;</span>
                <div id="profileForm"></div>
            </div>
>>>>>>> ef11096 (Key Improvements Confirmed)
        </div>
    </div>

    <script type="module" src="/app.mjs"></script>
</body>
<<<<<<< HEAD
<<<<<<< HEAD
</html>
=======
</html>
>>>>>>> cb8bffe (add frontend sample)
=======
</html>
>>>>>>> 82ec3cd (feat:add auth module)
