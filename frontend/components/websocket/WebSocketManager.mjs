/**
 * WebSocket Manager for real-time communication
 * Handles connection, reconnection, and message routing
 */
export class WebSocketManager {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.messageHandlers = new Map();
        this.connectionPromise = null;
        this.heartbeatInterval = null;
        
        // Bind methods to preserve context
        this.handleOpen = this.handleOpen.bind(this);
        this.handleMessage = this.handleMessage.bind(this);
        this.handleClose = this.handleClose.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * Connect to WebSocket server
     * @returns {Promise<boolean>} - Connection success status
     */
    async connect() {
        if (this.isConnected || this.connectionPromise) {
            return this.connectionPromise;
        }

        this.connectionPromise = new Promise((resolve, reject) => {
            try {
                // Determine WebSocket URL - connect to backend server
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const hostname = window.location.hostname;
                // Backend runs on port 8080, frontend on 8000
                const wsUrl = `${protocol}//${hostname}:8080/api/ws`;

                console.log(`🔌 Connecting to WebSocket: ${wsUrl}`);
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.handleOpen();
                    resolve(true);
                };
                
                this.ws.onmessage = this.handleMessage;
                this.ws.onclose = this.handleClose;
                this.ws.onerror = (error) => {
                    this.handleError(error);
                    reject(error);
                };

            } catch (error) {
                console.error('❌ WebSocket connection failed:', error);
                reject(error);
            }
        });

        return this.connectionPromise;
    }

    /**
     * Disconnect from WebSocket server
     */
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'User disconnected');
        }
        this.cleanup();
    }

    /**
     * Send a message through WebSocket
     * @param {string} type - Message type
     * @param {Object} data - Message data
     * @param {string} target - Target user ID (for direct messages)
     */
    send(type, data, target = null) {
        if (!this.isConnected) {
            console.warn('⚠️ WebSocket not connected, cannot send message');
            return false;
        }

        const message = {
            type: type,
            data: data,
            target: target,
            timestamp: new Date().toISOString()
        };

        try {
            this.ws.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('❌ Failed to send WebSocket message:', error);
            return false;
        }
    }

    /**
     * Register a message handler for a specific message type
     * @param {string} type - Message type to handle
     * @param {Function} handler - Handler function
     */
    onMessage(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }

    /**
     * Remove a message handler
     * @param {string} type - Message type
     * @param {Function} handler - Handler function to remove
     */
    offMessage(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Handle WebSocket connection open
     */
    handleOpen() {
        console.log('✅ WebSocket connected successfully');
        console.log('🔗 WebSocket readyState:', this.ws.readyState);
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
        this.connectionPromise = null;

        // Start heartbeat
        this.startHeartbeat();

        // Trigger connection handlers
        this.triggerHandlers('connect', { connected: true });

        console.log('📡 WebSocket connection fully established and ready');
    }

    /**
     * Handle incoming WebSocket messages
     * @param {MessageEvent} event - WebSocket message event
     */
    handleMessage(event) {
        try {
            console.log('📨 Raw WebSocket message received:', event.data);
            const message = JSON.parse(event.data);
            console.log('📨 Parsed WebSocket message:', message.type, message);
            console.log('📨 Message data:', message.data);

            // Check if we have handlers for this message type
            if (this.messageHandlers.has(message.type)) {
                console.log('✅ Found handlers for message type:', message.type);
                // Trigger handlers for this message type
                this.triggerHandlers(message.type, message.data);
            } else {
                console.log('⚠️ No handlers found for message type:', message.type);
                console.log('📋 Available handlers:', Array.from(this.messageHandlers.keys()));
            }

        } catch (error) {
            console.error('❌ Failed to parse WebSocket message:', error);
            console.error('❌ Raw message data:', event.data);
        }
    }

    /**
     * Handle WebSocket connection close
     * @param {CloseEvent} event - WebSocket close event
     */
    handleClose(event) {
        console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        this.cleanup();
        
        // Trigger disconnect handlers
        this.triggerHandlers('disconnect', { code: event.code, reason: event.reason });
        
        // Attempt reconnection if not a normal closure
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
    }

    /**
     * Handle WebSocket errors
     * @param {Event} error - WebSocket error event
     */
    handleError(error) {
        console.error('❌ WebSocket error:', error);
        this.triggerHandlers('error', { error });
    }

    /**
     * Attempt to reconnect to WebSocket
     */
    attemptReconnect() {
        this.reconnectAttempts++;
        console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect().catch(error => {
                console.error('❌ Reconnection failed:', error);
                
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('❌ Max reconnection attempts reached');
                    this.triggerHandlers('max_reconnect_attempts', {});
                }
            });
        }, this.reconnectDelay);
        
        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
    }

    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send('ping', {});
            }
        }, 30000); // Send ping every 30 seconds
    }

    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * Trigger handlers for a specific message type
     * @param {string} type - Message type
     * @param {Object} data - Message data
     */
    triggerHandlers(type, data) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            console.log(`🎯 Triggering ${handlers.length} handlers for type: ${type}`);
            handlers.forEach((handler, index) => {
                try {
                    console.log(`🎯 Calling handler ${index + 1} for ${type}`);
                    handler(data);
                    console.log(`✅ Handler ${index + 1} for ${type} completed`);
                } catch (error) {
                    console.error(`❌ Error in ${type} handler ${index + 1}:`, error);
                }
            });
        } else {
            console.log(`⚠️ No handlers registered for message type: ${type}`);
        }
    }

    /**
     * Clean up WebSocket connection
     */
    cleanup() {
        this.isConnected = false;
        this.connectionPromise = null;
        this.stopHeartbeat();
        
        if (this.ws) {
            this.ws.onopen = null;
            this.ws.onmessage = null;
            this.ws.onclose = null;
            this.ws.onerror = null;
            this.ws = null;
        }
    }

    /**
     * Get connection status
     * @returns {boolean} - Connection status
     */
    getConnectionStatus() {
        return this.isConnected;
    }
}
