/**
 * Online Users Manager
 * Handles display and management of online users
 */
export class OnlineUsersManager {
    constructor(webSocketManager) {
        this.wsManager = webSocketManager;
        this.onlineUsers = new Map();
        this.container = null;
        
        this.init();
    }

    /**
     * Initialize the online users manager
     */
    init() {
        console.log('🔌 Initializing OnlineUsersManager...');
        this.createContainer();
        this.setupWebSocketHandlers();
        console.log('✅ OnlineUsersManager initialized');
    }

    /**
     * Create the online users floating toggle at bottom right
     */
    createContainer() {
        console.log('📦 Creating online users floating toggle...');

        // Create the main container that will be positioned fixed at bottom right
        this.container = document.createElement('div');
        this.container.className = 'online-users-widget';
        this.container.innerHTML = `
            <!-- Toggle Button -->
            <div class="online-users-toggle" id="onlineUsersToggle">
                <div class="toggle-icon">
                    <span class="user-icon">👥</span>
                    <span class="online-count">0</span>
                </div>
                <div class="toggle-text">Online</div>
            </div>

            <!-- Expandable Panel -->
            <div class="online-users-panel" id="onlineUsersPanel">
                <div class="panel-header">
                    <h3>🟢 Online Users</h3>
                    <button class="close-panel" id="closePanelBtn">×</button>
                </div>
                <div class="panel-content">
                    <div class="online-users-list">
                        <div class="loading-users">Loading online users...</div>
                    </div>
                </div>
            </div>
        `;

        // Append to body so it's positioned relative to viewport
        document.body.appendChild(this.container);

        // Setup click handlers
        this.setupToggleHandlers();
        this.addStyles();
        console.log('✅ Online users floating widget created and added to DOM');
    }

    /**
     * Setup toggle button click handlers
     */
    setupToggleHandlers() {
        const toggleBtn = this.container.querySelector('#onlineUsersToggle');
        const panel = this.container.querySelector('#onlineUsersPanel');
        const closeBtn = this.container.querySelector('#closePanelBtn');

        // Toggle panel on button click
        toggleBtn.addEventListener('click', () => {
            this.togglePanel();
        });

        // Close panel on close button click
        closeBtn.addEventListener('click', () => {
            this.closePanel();
        });

        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target) && panel.classList.contains('expanded')) {
                this.closePanel();
            }
        });
    }

    /**
     * Toggle the online users panel
     */
    togglePanel() {
        const panel = this.container.querySelector('#onlineUsersPanel');
        const toggle = this.container.querySelector('#onlineUsersToggle');

        if (panel.classList.contains('expanded')) {
            this.closePanel();
        } else {
            this.openPanel();
        }
    }

    /**
     * Open the online users panel
     */
    openPanel() {
        const panel = this.container.querySelector('#onlineUsersPanel');
        const toggle = this.container.querySelector('#onlineUsersToggle');

        panel.classList.add('expanded');
        toggle.classList.add('active');

        // Add animation class
        panel.style.animation = 'slideUp 0.3s ease-out';
    }

    /**
     * Close the online users panel
     */
    closePanel() {
        const panel = this.container.querySelector('#onlineUsersPanel');
        const toggle = this.container.querySelector('#onlineUsersToggle');

        panel.classList.remove('expanded');
        toggle.classList.remove('active');

        // Add animation class
        panel.style.animation = 'slideDown 0.3s ease-out';
    }

    /**
     * Setup WebSocket message handlers
     */
    setupWebSocketHandlers() {
        // Handle initial online users list
        this.wsManager.onMessage('online_users', (data) => {
            this.updateOnlineUsersList(data.users);
            this.updateOnlineCount(data.count);
        });

        // Handle user status changes
        this.wsManager.onMessage('user_status', (data) => {
            this.handleUserStatusChange(data);
        });

        // Handle WebSocket connection
        this.wsManager.onMessage('connect', () => {
            this.showConnectedState();
        });

        // Handle WebSocket disconnection
        this.wsManager.onMessage('disconnect', () => {
            this.showDisconnectedState();
        });
    }

    /**
     * Update the online users list
     * @param {Array} users - Array of online users
     */
    updateOnlineUsersList(users) {
        const listContainer = this.container.querySelector('.online-users-list');
        
        if (!users || users.length === 0) {
            listContainer.innerHTML = '<div class="no-users">No other users online</div>';
            return;
        }

        // Update internal map
        this.onlineUsers.clear();
        users.forEach(user => {
            this.onlineUsers.set(user.user_id, user);
        });

        // Render users
        listContainer.innerHTML = users.map(user => this.renderUserItem(user)).join('');
    }

    /**
     * Handle user status change (online/offline)
     * @param {Object} userData - User status data
     */
    handleUserStatusChange(userData) {
        if (userData.status === 'online') {
            this.onlineUsers.set(userData.user_id, userData);
        } else {
            this.onlineUsers.delete(userData.user_id);
        }

        // Re-render the list
        const usersArray = Array.from(this.onlineUsers.values());
        this.updateOnlineUsersList(usersArray);
        this.updateOnlineCount(usersArray.length);
    }

    /**
     * Update the online users count
     * @param {number} count - Number of online users
     */
    updateOnlineCount(count) {
        const countElement = this.container.querySelector('.online-count');
        if (countElement) {
            countElement.textContent = count;
        }

        // Update toggle button text
        const toggleText = this.container.querySelector('.toggle-text');
        if (toggleText) {
            toggleText.textContent = count === 1 ? '1 Online' : `${count} Online`;
        }
    }

    /**
     * Render a single user item
     * @param {Object} user - User data
     * @returns {string} - HTML string for user item
     */
    renderUserItem(user) {
        const avatarUrl = user.avatar || '/static/pictures/default-avatar.png';
        return `
            <div class="online-user-item" data-user-id="${user.user_id}">
                <img src="${avatarUrl}" 
                     alt="${user.username}" 
                     class="user-avatar"
                     onerror="this.src='/static/pictures/default-avatar.png'">
                <div class="user-info">
                    <span class="username">${user.username}</span>
                    <span class="status-indicator online"></span>
                </div>
            </div>
        `;
    }

    /**
     * Show connected state
     */
    showConnectedState() {
        const header = this.container.querySelector('.panel-header h3');
        const toggle = this.container.querySelector('.online-users-toggle');

        if (header) {
            header.innerHTML = '🟢 Online Users';
        }

        if (toggle) {
            toggle.style.background = '#10b981';
        }
    }

    /**
     * Show disconnected state
     */
    showDisconnectedState() {
        const header = this.container.querySelector('.panel-header h3');
        const listContainer = this.container.querySelector('.online-users-list');
        const toggle = this.container.querySelector('.online-users-toggle');

        if (header) {
            header.innerHTML = '🔴 Online Users (Disconnected)';
        }

        if (listContainer) {
            listContainer.innerHTML = '<div class="disconnected">Connection lost. Reconnecting...</div>';
        }

        if (toggle) {
            toggle.style.background = '#ef4444'; // Red for disconnected
        }

        this.updateOnlineCount(0);
    }

    /**
     * Add CSS styles for floating online users widget
     */
    addStyles() {
        if (document.getElementById('online-users-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'online-users-styles';
        styles.textContent = `
            /* Main Widget Container */
            .online-users-widget {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            /* Toggle Button */
            .online-users-toggle {
                background: #10b981;
                color: white;
                border-radius: 50px;
                padding: 12px 16px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
                min-width: 80px;
                justify-content: center;
            }

            .online-users-toggle:hover {
                background: #059669;
                transform: translateY(-2px);
                box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
            }

            .online-users-toggle.active {
                border-radius: 12px 12px 0 0;
                background: #059669;
            }

            .toggle-icon {
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .user-icon {
                font-size: 16px;
            }

            .online-count {
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 6px;
                border-radius: 10px;
                font-size: 11px;
                font-weight: 600;
                min-width: 16px;
                text-align: center;
            }

            .toggle-text {
                font-size: 12px;
                font-weight: 500;
            }

            /* Expandable Panel */
            .online-users-panel {
                position: absolute;
                bottom: 100%;
                right: 0;
                width: 280px;
                max-height: 400px;
                background: white;
                border-radius: 12px 12px 0 0;
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
                transform: translateY(100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                border: 1px solid #e5e7eb;
            }

            .online-users-panel.expanded {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            /* Panel Header */
            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                border-radius: 12px 12px 0 0;
            }

            .panel-header h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #374151;
            }

            .close-panel {
                background: none;
                border: none;
                font-size: 20px;
                color: #6b7280;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s;
            }

            .close-panel:hover {
                background: #e5e7eb;
                color: #374151;
            }

            /* Panel Content */
            .panel-content {
                max-height: 320px;
                overflow-y: auto;
                padding: 8px;
            }

            .online-users-list {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            .online-user-item {
                display: flex;
                align-items: center;
                padding: 10px;
                border-radius: 8px;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .online-user-item:hover {
                background-color: #f3f4f6;
            }

            .user-avatar {
                width: 36px;
                height: 36px;
                border-radius: 50%;
                margin-right: 12px;
                object-fit: cover;
                border: 2px solid #e5e7eb;
            }

            .user-info {
                display: flex;
                flex-direction: column;
                flex: 1;
                position: relative;
            }

            .username {
                font-weight: 500;
                color: #374151;
                font-size: 14px;
                margin-bottom: 2px;
            }

            .status-indicator {
                position: absolute;
                top: 2px;
                right: 0;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                border: 2px solid white;
                background-color: #10b981;
            }

            .loading-users, .no-users, .disconnected {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 20px;
                font-size: 14px;
            }

            .disconnected {
                color: #ef4444;
            }

            /* Animations */
            @keyframes slideUp {
                from {
                    transform: translateY(100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            @keyframes slideDown {
                from {
                    transform: translateY(0);
                    opacity: 1;
                }
                to {
                    transform: translateY(100%);
                    opacity: 0;
                }
            }

            /* Mobile Responsive */
            @media (max-width: 768px) {
                .online-users-widget {
                    bottom: 80px; /* Above mobile nav if any */
                    right: 16px;
                }

                .online-users-panel {
                    width: 260px;
                    max-height: 350px;
                }

                .online-users-toggle {
                    padding: 10px 14px;
                }
            }

            /* Scrollbar Styling */
            .panel-content::-webkit-scrollbar {
                width: 6px;
            }

            .panel-content::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            .panel-content::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Get current online users
     * @returns {Array} - Array of online users
     */
    getOnlineUsers() {
        return Array.from(this.onlineUsers.values());
    }

    /**
     * Get online users count
     * @returns {number} - Number of online users
     */
    getOnlineCount() {
        return this.onlineUsers.size;
    }
}
