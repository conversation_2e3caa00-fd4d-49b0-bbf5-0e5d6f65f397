/**
 * Online Users Manager
 * Handles display and management of online users
 */
export class OnlineUsersManager {
    constructor(webSocketManager) {
        this.wsManager = webSocketManager;
        this.onlineUsers = new Map();
        this.container = null;
        
        this.init();
    }

    /**
     * Initialize the online users manager
     */
    init() {
        console.log('🔌 Initializing OnlineUsersManager...');
        this.createContainer();
        this.setupWebSocketHandlers();
        console.log('✅ OnlineUsersManager initialized');
    }

    /**
     * Create the online users container in the sidebar
     */
    createContainer() {
        console.log('📦 Creating online users container...');

        // Find the right sidebar or create one
        let rightSidebar = document.querySelector('.right-sidebar');
        console.log('🔍 Right sidebar found:', !!rightSidebar);

        if (!rightSidebar) {
            rightSidebar = document.createElement('div');
            rightSidebar.className = 'right-sidebar';
            document.querySelector('.container').appendChild(rightSidebar);
            console.log('➕ Created new right sidebar');
        }

        // Create online users section
        this.container = document.createElement('div');
        this.container.className = 'online-users-section';
        this.container.innerHTML = `
            <div class="online-users-header">
                <h3>🟢 Online Users</h3>
                <span class="online-count">0</span>
            </div>
            <div class="online-users-list">
                <div class="loading-users">Loading online users...</div>
            </div>
        `;

        rightSidebar.appendChild(this.container);
        this.addStyles();
        console.log('✅ Online users container created and added to DOM');
    }

    /**
     * Setup WebSocket message handlers
     */
    setupWebSocketHandlers() {
        // Handle initial online users list
        this.wsManager.onMessage('online_users', (data) => {
            this.updateOnlineUsersList(data.users);
            this.updateOnlineCount(data.count);
        });

        // Handle user status changes
        this.wsManager.onMessage('user_status', (data) => {
            this.handleUserStatusChange(data);
        });

        // Handle WebSocket connection
        this.wsManager.onMessage('connect', () => {
            this.showConnectedState();
        });

        // Handle WebSocket disconnection
        this.wsManager.onMessage('disconnect', () => {
            this.showDisconnectedState();
        });
    }

    /**
     * Update the online users list
     * @param {Array} users - Array of online users
     */
    updateOnlineUsersList(users) {
        const listContainer = this.container.querySelector('.online-users-list');
        
        if (!users || users.length === 0) {
            listContainer.innerHTML = '<div class="no-users">No other users online</div>';
            return;
        }

        // Update internal map
        this.onlineUsers.clear();
        users.forEach(user => {
            this.onlineUsers.set(user.user_id, user);
        });

        // Render users
        listContainer.innerHTML = users.map(user => this.renderUserItem(user)).join('');
    }

    /**
     * Handle user status change (online/offline)
     * @param {Object} userData - User status data
     */
    handleUserStatusChange(userData) {
        if (userData.status === 'online') {
            this.onlineUsers.set(userData.user_id, userData);
        } else {
            this.onlineUsers.delete(userData.user_id);
        }

        // Re-render the list
        const usersArray = Array.from(this.onlineUsers.values());
        this.updateOnlineUsersList(usersArray);
        this.updateOnlineCount(usersArray.length);
    }

    /**
     * Update the online users count
     * @param {number} count - Number of online users
     */
    updateOnlineCount(count) {
        const countElement = this.container.querySelector('.online-count');
        if (countElement) {
            countElement.textContent = count;
        }
    }

    /**
     * Render a single user item
     * @param {Object} user - User data
     * @returns {string} - HTML string for user item
     */
    renderUserItem(user) {
        const avatarUrl = user.avatar || '/static/pictures/default-avatar.png';
        return `
            <div class="online-user-item" data-user-id="${user.user_id}">
                <img src="${avatarUrl}" 
                     alt="${user.username}" 
                     class="user-avatar"
                     onerror="this.src='/static/pictures/default-avatar.png'">
                <div class="user-info">
                    <span class="username">${user.username}</span>
                    <span class="status-indicator online"></span>
                </div>
            </div>
        `;
    }

    /**
     * Show connected state
     */
    showConnectedState() {
        const header = this.container.querySelector('.online-users-header h3');
        if (header) {
            header.innerHTML = '🟢 Online Users';
        }
    }

    /**
     * Show disconnected state
     */
    showDisconnectedState() {
        const header = this.container.querySelector('.online-users-header h3');
        const listContainer = this.container.querySelector('.online-users-list');
        
        if (header) {
            header.innerHTML = '🔴 Online Users (Disconnected)';
        }
        
        if (listContainer) {
            listContainer.innerHTML = '<div class="disconnected">Connection lost. Reconnecting...</div>';
        }
        
        this.updateOnlineCount(0);
    }

    /**
     * Add CSS styles for online users
     */
    addStyles() {
        if (document.getElementById('online-users-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'online-users-styles';
        styles.textContent = `
            .online-users-section {
                background: white;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .online-users-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid #e5e7eb;
            }

            .online-users-header h3 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #374151;
            }

            .online-count {
                background: #10b981;
                color: white;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 600;
            }

            .online-users-list {
                max-height: 300px;
                overflow-y: auto;
            }

            .online-user-item {
                display: flex;
                align-items: center;
                padding: 8px;
                border-radius: 6px;
                margin-bottom: 4px;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .online-user-item:hover {
                background-color: #f3f4f6;
            }

            .user-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                margin-right: 10px;
                object-fit: cover;
            }

            .user-info {
                display: flex;
                flex-direction: column;
                flex: 1;
                position: relative;
            }

            .username {
                font-weight: 500;
                color: #374151;
                font-size: 14px;
            }

            .status-indicator {
                position: absolute;
                top: 0;
                right: 0;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                border: 2px solid white;
            }

            .status-indicator.online {
                background-color: #10b981;
            }

            .loading-users, .no-users, .disconnected {
                text-align: center;
                color: #6b7280;
                font-style: italic;
                padding: 20px;
            }

            .disconnected {
                color: #ef4444;
            }

            @media (max-width: 768px) {
                .right-sidebar {
                    display: none;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    /**
     * Get current online users
     * @returns {Array} - Array of online users
     */
    getOnlineUsers() {
        return Array.from(this.onlineUsers.values());
    }

    /**
     * Get online users count
     * @returns {number} - Number of online users
     */
    getOnlineCount() {
        return this.onlineUsers.size;
    }
}
