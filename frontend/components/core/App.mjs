/**
 * Main Application Controller - Orchestrates all components
 */
import { setupRouter } from '../router/Router.mjs';
import { AuthManager } from '../auth/AuthManager.mjs';
import { AuthModal } from '../auth/AuthModal.mjs';
import { NavManager } from '../navigation/NavManager.mjs';
import { MobileNavManager } from '../navigation/MobileNavManager.mjs';
import { CategoryManager } from '../categories/CategoryManager.mjs';
import { ReactionManager } from '../reactions/ReactionManager.mjs';
import { PostManager } from '../posts/PostManager.mjs';
import { PostForm } from '../posts/PostForm.mjs';
import { CommentManager } from '../comments/CommentManager.mjs';
import { NotificationManager } from '../notifications/NotificationManager.mjs';
import { ApiUtils } from '../utils/ApiUtils.mjs';

export class App {
    constructor() {
        this.authManager = null;
        this.authModal = null;
        this.navManager = null;
        this.mobileNavManager = null;
        this.categoryManager = null;
        this.reactionManager = null;
        this.postManager = null;
        this.postForm = null;
        this.commentManager = null;
        this.router = null;

        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            // Initialize notification system first
            this.notificationManager = new NotificationManager();

            // Set notification manager in ApiUtils for global error handling
            ApiUtils.setNotificationManager(this.notificationManager);

            // Initialize core managers
            this.authManager = new AuthManager();
            this.authModal = new AuthModal(this.authManager, (user) => this.onAuthSuccess(user), this.notificationManager);
            
            // Initialize navigation
            this.navManager = new NavManager(this.authManager, this.authModal, null, this.notificationManager);

            // Initialize mobile navigation
            this.mobileNavManager = new MobileNavManager(this);

            // Initialize category manager with filter callback
            this.categoryManager = new CategoryManager((categoryId) => this.onCategoryFilter(categoryId));
            
            // Initialize reaction manager
            this.reactionManager = new ReactionManager(this.authModal, this.notificationManager);
            
            // Initialize comment manager
            this.commentManager = new CommentManager(this.authModal, this.reactionManager, this.notificationManager);
            
            // Initialize post manager
            this.postManager = new PostManager(this.reactionManager, this.commentManager);
            
            // Initialize post form
            this.postForm = new PostForm(
                this.categoryManager,
                this.authModal,
                () => this.onPostCreated(),
                this.notificationManager
            );

            // Setup router first
            console.log('App: Setting up router');
            this.router = setupRouter(this);
            console.log('App: Router created:', !!this.router);

            // Set router reference in navigation manager
            console.log('App: Setting router in navigation manager');
            this.navManager.setRouter(this.router);
            console.log('App: Router set in navigation manager');

            // Set router reference in category manager
            console.log('App: Setting router in category manager');
            this.categoryManager.setRouter(this.router);
            console.log('App: Router set in category manager');

            // Set router reference in post manager
            console.log('App: Setting router in post manager');
            this.postManager.setRouter(this.router);
            console.log('App: Router set in post manager');

            // Set app reference in post manager
            console.log('App: Setting app reference in post manager');
            this.postManager.setApp(this);
            console.log('App: App reference set in post manager');

            // Setup the application
            await this.setupApp();
            console.log('Forum application initialized successfully');
        } catch (error) {
            console.error('Error initializing application:', error);
        }
    }

    /**
     * Setup the application components
     */
    async setupApp() {
        // Check authentication status first
        const isAuthenticated = await this.authManager.checkAuthStatus();

        if (!isAuthenticated) {
            // User is not authenticated, show login modal immediately
            this.showLoginRequired();
            return;
        }

        // User is authenticated, proceed with normal setup
        await this.setupAuthenticatedApp();
    }

    /**
     * Show login required modal and prevent access to content
     */
    showLoginRequired() {
        // Set appropriate page title
        document.title = 'Forum - Login Required';

        // Hide main content
        this.hideMainContent();

        // Show login modal with message that login is required
        this.authModal.showLoginModal(true); // true indicates login is required

        // Listen for authentication state changes
        this.authManager.onAuthStateChange = (isAuthenticated) => {
            if (isAuthenticated) {
                this.setupAuthenticatedApp();
            } else {
                // User logged out, show login required again
                this.showLoginRequired();
            }
        };
    }

    /**
     * Setup the application for authenticated users
     */
    async setupAuthenticatedApp() {
        // Set default title for authenticated users
        document.title = 'Forum - Home';

        // Show main content
        this.showMainContent();

        // Start the router now that user is authenticated
        if (this.router) {
            this.router.start();
        }

        // Render categories in sidebar
        await this.categoryManager.renderCategories();

        // Setup authentication UI
        await this.navManager.setupAuthButtons();

        // Note: Posts and main content will be rendered by the router based on the current route
    }

    /**
     * Hide main content when user is not authenticated
     */
    hideMainContent() {
        const mainContent = document.querySelector('.main-content');
        const leftSidebar = document.querySelector('.left-sidebar');
        const rightSidebar = document.querySelector('.right-sidebar');
        const navbar = document.querySelector('.navbar');
        const container = document.querySelector('.container');

        if (mainContent) mainContent.style.display = 'none';
        if (leftSidebar) leftSidebar.style.display = 'none';
        if (rightSidebar) rightSidebar.style.display = 'none';
        if (navbar) navbar.style.display = 'none';
        if (container) container.style.display = 'none';
    }

    /**
     * Show main content when user is authenticated
     */
    showMainContent() {
        const mainContent = document.querySelector('.main-content');
        const leftSidebar = document.querySelector('.left-sidebar');
        const rightSidebar = document.querySelector('.right-sidebar');
        const navbar = document.querySelector('.navbar');
        const container = document.querySelector('.container');

        if (mainContent) mainContent.style.display = '';
        if (leftSidebar) leftSidebar.style.display = '';
        if (rightSidebar) rightSidebar.style.display = '';
        if (navbar) navbar.style.display = '';
        if (container) container.style.display = '';
    }

    /**
     * Handle successful authentication
     * @param {Object} user - Authenticated user data
     */
    onAuthSuccess(user) {
        console.log('User authenticated:', user.username);
        this.navManager.onAuthSuccess(user);
    }

    /**
     * Handle category filter changes
     * @param {number} categoryId - Selected category ID
     */
    async onCategoryFilter(categoryId) {
        // Navigate to home with category filter or category-specific route
        if (categoryId) {
            // Option 1: Navigate to category-specific route
            this.router.navigate(`/category/${categoryId}`);

            // Option 2: Navigate to home with category query parameter
            // this.router.navigate(`/?category=${categoryId}`);
        } else {
            // Navigate to home without filter
            this.router.navigate('/');
        }
    }

    /**
     * Handle post creation
     */
    async onPostCreated() {
        console.log('New post created, refreshing feed...');
        await this.postManager.refreshPosts();
    }

    /**
     * Get the auth manager instance
     * @returns {AuthManager} - Auth manager instance
     */
    getAuthManager() {
        return this.authManager;
    }

    /**
     * Get the post manager instance
     * @returns {PostManager} - Post manager instance
     */
    getPostManager() {
        return this.postManager;
    }

    /**
     * Get the category manager instance
     * @returns {CategoryManager} - Category manager instance
     */
    getCategoryManager() {
        return this.categoryManager;
    }

    /**
     * Get the comment manager instance
     * @returns {CommentManager} - Comment manager instance
     */
    getCommentManager() {
        return this.commentManager;
    }

    /**
     * Get the reaction manager instance
     * @returns {ReactionManager} - Reaction manager instance
     */
    getReactionManager() {
        return this.reactionManager;
    }

    /**
     * Get the notification manager instance
     * @returns {NotificationManager} - Notification manager instance
     */
    getNotificationManager() {
        return this.notificationManager;
    }

    /**
     * Refresh the entire application
     */
    async refresh() {
        await this.postManager.refreshPosts();
        await this.categoryManager.renderCategories();
        await this.navManager.setupAuthButtons();
    }

    /**
     * Handle application errors
     * @param {Error} error - Error to handle
     * @param {string} context - Context where error occurred
     */
    handleError(error, context = '') {
        console.error(`Application error in ${context}:`, error);

        // Show error notification using the notification system
        if (this.notificationManager) {
            this.notificationManager.error(`An error occurred${context ? ` in ${context}` : ''}: ${error.message}`);
        }
    }
}
