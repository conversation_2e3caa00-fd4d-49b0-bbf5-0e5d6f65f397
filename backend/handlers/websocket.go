package handlers

import (
	"database/sql"
	"forum/utils"
	"forum/websocket"
	"log"
	"net/http"
)

// WebSocketHandler handles WebSocket connection upgrades
func WebSocketHandler(db *sql.DB, hub *websocket.Hub) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get user ID from session
		userID, err := utils.GetUserIDFromSession(db, r)
		if err != nil {
			log.Printf("WebSocket connection denied - no valid session: %v", err)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Get user details from database
		user, err := getUserByID(db, userID)
		if err != nil {
			log.Printf("WebSocket connection denied - user not found: %v", err)
			http.Error(w, "User not found", http.StatusNotFound)
			return
		}

		log.Printf("WebSocket connection request from user: %s (%s)", user.Username, userID)

		// Upgrade the HTTP connection to WebSocket
		websocket.ServeWS(hub, w, r, userID, user.Username, user.AvatarURL)
	}
}

// getUserByID retrieves user information by ID
func getUserByID(db *sql.DB, userID string) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, email, avatar_url, created_at, updated_at
		FROM users 
		WHERE id = ?
	`
	
	err := db.QueryRow(query, userID).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.AvatarURL,
		&user.CreatedAt,
		&user.UpdatedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	return user, nil
}

// User represents a user for WebSocket purposes
type User struct {
	ID        string `json:"id"`
	Username  string `json:"username"`
	Email     string `json:"email"`
	AvatarURL string `json:"avatar_url"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}
