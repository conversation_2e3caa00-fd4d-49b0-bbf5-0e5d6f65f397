package websocket

import (
	"encoding/json"
	"time"
)

// WSMessage represents a WebSocket message
type WSMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	UserID    string      `json:"user_id,omitempty"`
	Target    string      `json:"target,omitempty"` // for direct messages
	Timestamp time.Time   `json:"timestamp"`
}

// Message types constants
const (
	// Connection management
	MessageTypeConnect    = "connect"
	MessageTypeDisconnect = "disconnect"
	MessageTypePing       = "ping"
	MessageTypePong       = "pong"

	// User status
	MessageTypeUserStatus  = "user_status"
	MessageTypeOnlineUsers = "online_users"

	// Notifications
	MessageTypeNotification = "notification"

	// Direct messaging
	MessageTypeDirectMessage = "direct_message"
	MessageTypeMessageRead   = "message_read"

	// Live updates
	MessageTypePostUpdate    = "post_update"
	MessageTypeCommentUpdate = "comment_update"
	MessageTypeLikeUpdate    = "like_update"
)

// UserStatusData represents user online/offline status
type UserStatusData struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Avatar   string `json:"avatar"`
	Status   string `json:"status"` // "online" or "offline"
}

// OnlineUsersData represents list of online users
type OnlineUsersData struct {
	Users []UserStatusData `json:"users"`
	Count int              `json:"count"`
}

// NotificationData represents a real-time notification
type NotificationData struct {
	ID        int       `json:"id"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	Type      string    `json:"type"` // "like", "comment", "mention", "message"
	RelatedID int       `json:"related_id,omitempty"`
	CreatedAt time.Time `json:"created_at"`
}

// DirectMessageData represents a direct message
type DirectMessageData struct {
	ID         int       `json:"id"`
	SenderID   string    `json:"sender_id"`
	Sender     string    `json:"sender"`
	SenderAvatar string  `json:"sender_avatar"`
	Content    string    `json:"content"`
	CreatedAt  time.Time `json:"created_at"`
}

// PostUpdateData represents live post updates
type PostUpdateData struct {
	Action string      `json:"action"` // "new_post", "post_liked", "post_unliked"
	PostID int         `json:"post_id"`
	Post   interface{} `json:"post,omitempty"`   // full post data for new posts
	User   interface{} `json:"user,omitempty"`   // user who performed action
	Count  int         `json:"count,omitempty"`  // like count for like updates
}

// CommentUpdateData represents live comment updates
type CommentUpdateData struct {
	Action    string      `json:"action"` // "new_comment", "comment_liked"
	PostID    int         `json:"post_id"`
	CommentID int         `json:"comment_id,omitempty"`
	Comment   interface{} `json:"comment,omitempty"` // full comment data for new comments
	User      interface{} `json:"user,omitempty"`    // user who performed action
}

// NewWSMessage creates a new WebSocket message
func NewWSMessage(msgType string, data interface{}) *WSMessage {
	return &WSMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// NewTargetedWSMessage creates a new targeted WebSocket message
func NewTargetedWSMessage(msgType string, data interface{}, target string) *WSMessage {
	return &WSMessage{
		Type:      msgType,
		Data:      data,
		Target:    target,
		Timestamp: time.Now(),
	}
}

// ToJSON converts the message to JSON bytes
func (m *WSMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON creates a WSMessage from JSON bytes
func FromJSON(data []byte) (*WSMessage, error) {
	var msg WSMessage
	err := json.Unmarshal(data, &msg)
	if err != nil {
		return nil, err
	}
	return &msg, nil
}
