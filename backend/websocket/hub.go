package websocket

import (
	"database/sql"
	"log"
	"time"
)

// <PERSON><PERSON> maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Map of user ID to client for direct messaging
	userClients map[string]*Client

	// Inbound messages from the clients
	broadcast chan []byte

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Database connection for storing messages and notifications
	db *sql.DB
}

// NewHub creates a new WebSocket hub
func NewHub(db *sql.DB) *Hub {
	return &Hub{
		clients:     make(map[*Client]bool),
		userClients: make(map[string]*Client),
		broadcast:   make(chan []byte),
		register:    make(chan *Client),
		unregister:  make(chan *Client),
		db:          db,
	}
}

// Run starts the hub and handles client registration/unregistration
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.clients[client] = true
			h.userClients[client.userID] = client

			log.Printf("✅ User %s (%s) connected via WebSocket", client.username, client.userID)
			log.Printf("📊 Total clients now: %d", len(h.clients))

			// Update user online status in database
			h.updateUserOnlineStatus(client.userID, true)

			// Send current online users to the new client
			log.Printf("📤 Sending online users list to new client: %s", client.username)
			h.sendOnlineUsersToClient(client)

			// Broadcast user status change to all clients
			log.Printf("📢 Broadcasting user status change: %s is online", client.username)
			h.broadcastUserStatus(client, "online")

		case client := <-h.unregister:
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				delete(h.userClients, client.userID)
				close(client.send)

				log.Printf("User %s (%s) disconnected from WebSocket", client.username, client.userID)

				// Update user online status in database
				h.updateUserOnlineStatus(client.userID, false)

				// Broadcast user status change to all clients
				h.broadcastUserStatus(client, "offline")
			}

		case message := <-h.broadcast:
			// Broadcast message to all clients
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
					delete(h.userClients, client.userID)
				}
			}
		}
	}
}

// BroadcastMessage sends a message to all connected clients
func (h *Hub) BroadcastMessage(msg *WSMessage) {
	data, err := msg.ToJSON()
	if err != nil {
		log.Printf("Error marshaling broadcast message: %v", err)
		return
	}

	h.broadcast <- data
}

// SendMessageToUser sends a message to a specific user
func (h *Hub) SendMessageToUser(userID string, msg *WSMessage) {
	if client, ok := h.userClients[userID]; ok {
		client.sendMessage(msg)
	}
}

// broadcastUserStatus broadcasts user online/offline status to all clients
func (h *Hub) broadcastUserStatus(client *Client, status string) {
	statusData := UserStatusData{
		UserID:   client.userID,
		Username: client.username,
		Avatar:   client.avatar,
		Status:   status,
	}

	msg := NewWSMessage(MessageTypeUserStatus, statusData)
	h.BroadcastMessage(msg)
}

// sendOnlineUsersToClient sends the list of online users to a specific client
func (h *Hub) sendOnlineUsersToClient(client *Client) {
	var onlineUsers []UserStatusData

	log.Printf("📊 Total connected clients: %d", len(h.userClients))

	for _, c := range h.userClients {
		log.Printf("🔍 Checking client: %s (%s)", c.username, c.userID)
		if c.userID != client.userID { // Don't include the requesting user
			onlineUsers = append(onlineUsers, UserStatusData{
				UserID:   c.userID,
				Username: c.username,
				Avatar:   c.avatar,
				Status:   "online",
			})
			log.Printf("➕ Added to online users: %s", c.username)
		} else {
			log.Printf("⏭️ Skipping self: %s", c.username)
		}
	}

	onlineUsersData := OnlineUsersData{
		Users: onlineUsers,
		Count: len(onlineUsers),
	}

	log.Printf("📤 Sending online users to %s: %d users", client.username, len(onlineUsers))
	msg := NewWSMessage(MessageTypeOnlineUsers, onlineUsersData)
	client.sendMessage(msg)
}

// GetOnlineUsers returns the list of currently online users
func (h *Hub) GetOnlineUsers() []UserStatusData {
	var onlineUsers []UserStatusData

	for _, client := range h.userClients {
		onlineUsers = append(onlineUsers, UserStatusData{
			UserID:   client.userID,
			Username: client.username,
			Avatar:   client.avatar,
			Status:   "online",
		})
	}

	return onlineUsers
}

// GetOnlineUserCount returns the number of currently online users
func (h *Hub) GetOnlineUserCount() int {
	return len(h.userClients)
}

// handleDirectMessage handles direct messages between users
func (h *Hub) handleDirectMessage(sender *Client, msg *WSMessage) {
	// This will be implemented in Phase 4
	log.Printf("Direct message from %s: %+v", sender.username, msg)
}

// updateUserOnlineStatus updates the user's online status in the database
func (h *Hub) updateUserOnlineStatus(userID string, isOnline bool) {
	query := `
		INSERT OR REPLACE INTO user_sessions_ws (user_id, last_seen, is_online)
		VALUES (?, ?, ?)
	`
	_, err := h.db.Exec(query, userID, time.Now(), isOnline)
	if err != nil {
		log.Printf("Error updating user online status: %v", err)
	}
}

// BroadcastNotification sends a notification to a specific user
func (h *Hub) BroadcastNotification(userID string, notification NotificationData) {
	msg := NewWSMessage(MessageTypeNotification, notification)
	h.SendMessageToUser(userID, msg)
}

// BroadcastPostUpdate broadcasts post updates to all users
func (h *Hub) BroadcastPostUpdate(postUpdate PostUpdateData) {
	msg := NewWSMessage(MessageTypePostUpdate, postUpdate)
	h.BroadcastMessage(msg)
}

// BroadcastCommentUpdate broadcasts comment updates to all users
func (h *Hub) BroadcastCommentUpdate(commentUpdate CommentUpdateData) {
	msg := NewWSMessage(MessageTypeCommentUpdate, commentUpdate)
	h.BroadcastMessage(msg)
}
