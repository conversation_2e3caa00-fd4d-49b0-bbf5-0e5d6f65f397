package websocket

import (
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 120 * time.Second // Increased from 60s

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 1024 // Increased from 512
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow connections from any origin for now
		// In production, you should validate the origin
		return true
	},
}

// Client represents a WebSocket client connection
type Client struct {
	hub      *Hub
	conn     *websocket.Conn
	send     chan []byte
	userID   string
	username string
	avatar   string
}

// NewClient creates a new WebSocket client
func NewClient(hub *Hub, conn *websocket.Conn, userID, username, avatar string) *Client {
	return &Client{
		hub:      hub,
		conn:     conn,
		send:     make(chan []byte, 1024), // Increased buffer size
		userID:   userID,
		username: username,
		avatar:   avatar,
	}
}

// readPump pumps messages from the WebSocket connection to the hub
func (c *Client) readPump() {
	log.Printf("📖 Starting readPump for %s", c.username)
	defer func() {
		log.Printf("📖 readPump ending for %s", c.username)
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("❌ WebSocket unexpected close for %s: %v", c.username, err)
			} else {
				log.Printf("📖 WebSocket normal close for %s: %v", c.username, err)
			}
			break
		}

		// Parse the incoming message
		wsMsg, err := FromJSON(message)
		if err != nil {
			log.Printf("Error parsing WebSocket message: %v", err)
			continue
		}

		// Set the user ID for the message
		wsMsg.UserID = c.userID

		// Handle the message
		c.handleMessage(wsMsg)
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued chat messages to the current WebSocket message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processes incoming WebSocket messages
func (c *Client) handleMessage(msg *WSMessage) {
	switch msg.Type {
	case MessageTypePing:
		// Respond with pong
		pongMsg := NewWSMessage(MessageTypePong, nil)
		c.sendMessage(pongMsg)

	case MessageTypeDirectMessage:
		// Handle direct message
		c.hub.handleDirectMessage(c, msg)

	default:
		log.Printf("Unknown message type: %s", msg.Type)
	}
}

// sendMessage sends a message to this client
func (c *Client) sendMessage(msg *WSMessage) {
	data, err := msg.ToJSON()
	if err != nil {
		log.Printf("❌ Error marshaling message for %s: %v", c.username, err)
		return
	}

	select {
	case c.send <- data:
		log.Printf("✅ Message sent to %s: %s", c.username, msg.Type)
	default:
		log.Printf("⚠️ Send channel full for %s, closing connection", c.username)
		close(c.send)
		delete(c.hub.clients, c)
		delete(c.hub.userClients, c.userID)
	}
}

// ServeWS handles WebSocket requests from the peer
func ServeWS(hub *Hub, w http.ResponseWriter, r *http.Request, userID, username, avatar string) {
	log.Printf("🔄 Starting WebSocket upgrade for %s (%s)", username, userID)

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("❌ WebSocket upgrade error for %s: %v", username, err)
		return
	}

	log.Printf("✅ WebSocket upgrade successful for %s", username)

	client := NewClient(hub, conn, userID, username, avatar)
	log.Printf("📝 Created new client for %s", username)

	// Register client with hub in a goroutine to prevent blocking
	go func() {
		log.Printf("📤 Registering client %s with hub", username)
		client.hub.register <- client
		log.Printf("✅ Client %s registered with hub", username)
	}()

	// Allow collection of memory referenced by the caller by doing all work in new goroutines
	log.Printf("🚀 Starting goroutines for %s", username)
	go client.writePump()
	go client.readPump()
	log.Printf("✅ Goroutines started for %s", username)
}
