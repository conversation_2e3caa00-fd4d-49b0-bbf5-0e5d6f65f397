package main

import (
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"forum/middleware"
	"forum/routes"
	"forum/sqlite"
<<<<<<< HEAD
=======
	"log"
	"forum/sqlite"
	"fmt"
	"forum/models"
>>>>>>> 9243179 (application entrypoint)
)

func main() {
<<<<<<< HEAD
	// Initialize the database
<<<<<<< HEAD
=======
	// Validate CLI args
>>>>>>> 57718de (serve static files securely)
=======
)

func main() {
<<<<<<< HEAD
	// Initialize the database
>>>>>>> 6b85c40 (backend connection)
=======
	// Validate CLI args
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
	if len(os.Args) > 2 {
		fmt.Println("Usage:\n\n$ go run .\n\nor\n\n$ go run . 'port no'\n\nwhere port no; is a four digit integer greater than 1023 and not equal to 3306/3389")
		return
	}
	port := ":8080"
	if len(os.Args) == 2 {
		p, er := strconv.Atoi(os.Args[1])
		if er != nil || !(p > 1023 && p < 65536 && p != 3306 && p != 3389) {
			fmt.Println("Usage:\n\n$ go run .\n\nor\n\n$ go run . 'port no'\n\nwhere port no; is a four digit integer greater than 1023 and not equal to 3306/3389")
			return
		}
		port = ":" + os.Args[1]
	}
<<<<<<< HEAD
<<<<<<< HEAD

	// Initialize the database
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
=======

	// Initialize the database
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
	err := sqlite.InitializeDatabase("forum.db")
=======
	dbPath := os.Getenv("DB_PATH")
	if dbPath == "" {
		dbPath = "forum.db" // fallback default
	}
	err := sqlite.InitializeDatabase(dbPath)
>>>>>>> 1b81024 (Fix Docker containerization issues)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer sqlite.CloseDatabase()

<<<<<<< HEAD
<<<<<<< HEAD
	// Serve static files securely (prevent directory listing)
	// fs := http.FileServer(http.Dir("./static"))
	// http.Handle("/static/", http.StripPrefix("/static/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 	if r.URL.Path == "/" || r.URL.Path == "" || r.URL.Path[len(r.URL.Path)-1] == '/' {
	// 		log.Printf("⚠️  Directory listing blocked: /static/%s", r.URL.Path)
	// 		http.Error(w, "Access Denid", http.StatusUnauthorized)
	// 		fmt.Print("called")
	// 		return
	// 	}
	// 	fs.ServeHTTP(w, r)
	// })))

	// Set up routes and CORS
	mux := routes.SetupRoutes(sqlite.DB)
	handler := middleware.CORS(mux)

<<<<<<< HEAD
	// Start daily session cleanup in background
	go scheduleDailyCleanup()

	// Start server
=======
	// Setup routes
=======
	// Set up routes and CORS
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
	mux := routes.SetupRoutes(sqlite.DB)
	handler := middleware.CORS(mux)

	// Serve static files securely (prevent directory listing)
	fs := http.FileServer(http.Dir("./static"))
	http.Handle("/static/", http.StripPrefix("/static/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" || r.URL.Path == "" || r.URL.Path[len(r.URL.Path)-1] == '/' {
			log.Printf("⚠️  Directory listing blocked: /static/%s", r.URL.Path)
			http.NotFound(w, r)
			return
		}
		fs.ServeHTTP(w, r)
	})))

=======
>>>>>>> f42fe81 (chore: migrating directory listing prevention to route.go file.)
	// Start daily session cleanup in background
	go scheduleDailyCleanup()

	// Start server
<<<<<<< HEAD

>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
	fmt.Printf("🚀 [%s] Server is running at http://localhost%s\n", time.Now().Format(time.RFC3339), port)
	log.Fatal(http.ListenAndServe(port, handler))
}

// scheduleDailyCleanup runs session cleanup at midnight every day
func scheduleDailyCleanup() {
	for {
<<<<<<< HEAD
<<<<<<< HEAD
=======
		// Calculate the duration until next midnight
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
		now := time.Now()
		nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
		sleepDuration := time.Until(nextMidnight)

		fmt.Println("🕛 Session cleanup scheduled for midnight...")
<<<<<<< HEAD
<<<<<<< HEAD
		for remaining := sleepDuration; remaining > 0; remaining -= time.Minute {
			hours := int(remaining.Hours())
			minutes := int(remaining.Minutes()) % 60
			fmt.Printf("\r⏳ Time until cleanup: %02d h %02d min", hours, minutes)
=======

		// Start countdown in hours and minutes
		for remaining := sleepDuration; remaining > 0; remaining -= time.Minute {
			hours := int(remaining.Hours())
			minutes := int(remaining.Minutes()) % 60
			fmt.Printf("\r⏳ Time until cleanup: %02d h %02d min", hours, minutes) // Overwrites same line
>>>>>>> 6b85c40 (backend connection)
=======
		for remaining := sleepDuration; remaining > 0; remaining -= time.Minute {
			hours := int(remaining.Hours())
			minutes := int(remaining.Minutes()) % 60
			fmt.Printf("\r⏳ Time until cleanup: %02d h %02d min", hours, minutes)
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
			time.Sleep(1 * time.Minute)
		}

		fmt.Println("\n🚀 Running session cleanup...")
<<<<<<< HEAD
<<<<<<< HEAD
=======

		// Run session cleanup
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
		if err := sqlite.CleanupSessions(sqlite.DB, 24); err != nil {
			fmt.Printf("❌ [%s] Session cleanup failed: %v\n", time.Now().Format(time.RFC3339), err)
		} else {
			fmt.Println("✅ Expired sessions cleaned up successfully at midnight.")
		}
	}
<<<<<<< HEAD
=======
	err := sqlite.InitDB("database.db")
	if err != nil {
		log.Fatal("Failed to initialize the database:", err)
	}

	// Ensure the database closes when the program exits
	defer sqlite.CloseDB()

	log.Println("Database setup complete!")

	    // Create a new user
		user := models.User{
			Username: "john",
			Email:    "<EMAIL>",
			Password: "password",
		}
	
		// Create a new post
		post := models.Post{
			Title:    "Hello World",
			Content:  "This is a sample post",
			UserID:   1,
			CategoryID: 1,
		}
	
		// Create a new comment
		comment := models.Comment{
			Content:  "This is a sample comment",
			PostID:   1,
			UserID:   1,
		}
	
		// Create a new category
		category := models.Category{
			Name:     "Sample Category",
		}
	
		fmt.Println("User:", user)
		fmt.Println("Post:", post)
		fmt.Println("Comment:", comment)
		fmt.Println("Category:", category)
>>>>>>> 9243179 (application entrypoint)
=======
>>>>>>> 6b85c40 (backend connection)
}
