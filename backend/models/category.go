package models

type Category struct {
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
	ID   int    `json:"id" gorm:"primaryKey"`
	Name string `json:"name" validate:"required" gorm:"unique;not null"`
}
=======
    ID       int    `json:"id"`
    Name     string `json:"name"`
}
>>>>>>> 887cfac (models to backend(fs))
=======
    ID       int    `json:"id" validate:"required"`
    Name     string `json:"name" validate:"required,min=3,max=32"`
}
>>>>>>> b814b17 (Update backend/models/category.go)
=======
	ID   int    `json:"id" gorm:"primaryKey"`
	Name string `json:"name" validate:"required" gorm:"unique;not null"`
}
>>>>>>> 04e48a3 (update on category)
=======
	ID   int    `json:"id" gorm:"primaryKey"`
	Name string `json:"name" validate:"required" gorm:"unique;not null"`
}
>>>>>>> 6b85c40 (backend connection)
