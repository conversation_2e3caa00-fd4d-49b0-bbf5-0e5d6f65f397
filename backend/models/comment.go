package models

<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
import "time"

type Comment struct {
	ID            int            `json:"id" gorm:"primaryKey"`
	UserID        string         `json:"user_id" validate:"required" gorm:"not null"`
	UserName      string         `json:"username"`
	ProfileAvatar string         `json:"avatar_url"`
	PostID        int            `json:"post_id,omitempty"`
	Content       string         `json:"content" validate:"required" gorm:"not null"`
	CreatedAt     time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	Replies       []ReplyComment `json:"replies,omitempty" gorm:"-"`
}
<<<<<<< HEAD
<<<<<<< HEAD
=======
type Comment struct {
<<<<<<< HEAD
    ID       int    `json:"id"`
    Content  string `json:"content"`
    PostID   int    `json:"post_id"`
    UserID   int    `json:"user_id"`
}
>>>>>>> 887cfac (models to backend(fs))
=======
    ID       int    `json:"id" validate:"required"`
    Content  string `json:"content" validate:"required,min=3,max=1024"`
    PostID   int    `json:"post_id" validate:"required"`
    UserID   int    `json:"user_id" validate:"required"`
}
>>>>>>> 367ed82 (Update backend/models/comment.go)
=======
>>>>>>> 6b85c40 (backend connection)
=======

type ReplyComment struct {
	ID              int       `json:"id" gorm:"primaryKey"`
	UserID          string    `json:"user_id" validate:"required" gorm:"not null"`
	UserName        string    `json:"username"`
	ProfileAvatar   string    `json:"avatar_url"`
	ParentCommentID int       `json:"parent_comment_id,omitempty"`
	Content         string    `json:"content" validate:"required" gorm:"not null"`
	CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
>>>>>>> 93fc676 (add: commentreply struct)
