package models

<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
import "time"

type User struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	Username     string    `json:"username" gorm:"unique;not null"`
	Email        string    `json:"email" gorm:"unique;not null"`
<<<<<<< HEAD
<<<<<<< HEAD
	PasswordHash string    `json:"-" gorm:"not null"`
	AvatarURL    string    `json:"avatar_url" gorm:"default:'/static/default-avatar.png'"` // ✅ New field
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
=======
type User struct {
<<<<<<< HEAD
    ID       int    `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
    Password string `json:"password"`
}
>>>>>>> 887cfac (models to backend(fs))
=======
    ID       int    `json:"id" validate:"required"`
    Username string `json:"username" validate:"required,min=3,max=32"`
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8,max=128"`
}
>>>>>>> a887e0f (Update backend/models/user.go)
=======
	PasswordHash string    `json:"-" gorm:"not null"` // ✅ Ensure this field exists
=======
	PasswordHash string    `json:"-" gorm:"not null"`
	AvatarURL    string    `json:"avatar_url" gorm:"default:'/static/default-avatar.png'"` // ✅ New field
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
>>>>>>> 6b85c40 (backend connection)
