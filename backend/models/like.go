package models

type Like struct {
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> 2e5b524 (modified:   backend/models/like.go)
	ID        int  `json:"id" gorm:"primaryKey"`
	UserID    int  `json:"user_id" validate:"required" gorm:"not null"`
	PostID    *int `json:"post_id,omitempty"`
	CommentID *int `json:"comment_id,omitempty"` // Allows NULL for post likes
	IsLike    bool `json:"is_like" validate:"required" gorm:"not null"`
=======
	UserID    int     `json:"user_id" validate:"required"`
	PostID    *int    `json:"post_id,omitempty"`
	CommentID *int    `json:"comment_id,omitempty"`
	Type      string  `json:"type" validate:"required,oneof=like dislike"` // must be "like" or "dislike"
>>>>>>> 3996ff2 (change like struct to have field of type dislike/like)
=======
	UserID    int    `json:"user_id" validate:"required"`
=======
	UserID    string   `json:"user_id" validate:"required"`
>>>>>>> 9ebc57b (updating category id to string data type)
=======
	UserID    string `json:"user_id" validate:"required"`
>>>>>>> 2216737 (formatting of code)
	PostID    *int   `json:"post_id,omitempty"`
	CommentID *int   `json:"comment_id,omitempty"`
	Type      string `json:"type" validate:"required,oneof=like dislike"` // must be "like" or "dislike"
>>>>>>> 20f81a2 (formating of code)
}
<<<<<<< HEAD
=======
    ID       int    `json:"id" validate:"required"`
    UserID   int    `json:"user_id" validate:"required"`
    PostID   int    `json:"post_id" validate:"required"`
    CommentID int    `json:"comment_id" validate:"required"`
    IsLike   bool   `json:"is_like" validate:"required"`
}
>>>>>>> 900f37a (Add backend/models/like.go)
=======
>>>>>>> 2e5b524 (modified:   backend/models/like.go)
=======
	ID        int  `json:"id" gorm:"primaryKey"`
	UserID    int  `json:"user_id" validate:"required" gorm:"not null"`
	PostID    *int `json:"post_id,omitempty"`
	CommentID *int `json:"comment_id,omitempty"` // Allows NULL for post likes
	IsLike    bool `json:"is_like" validate:"required" gorm:"not null"`
}
>>>>>>> 6b85c40 (backend connection)
