package models

<<<<<<< HEAD
<<<<<<< HEAD
import "time"

type Post struct {
	ID            int       `json:"id" gorm:"primaryKey"`
	ProfileAvatar string    `json:"avatar_url"`
	Title         string    `json:"title" validate:"required" gorm:"not null"`
	Content       string    `json:"content" validate:"required" gorm:"not null"`
	Username      string    `json:"username" gorm:"-"`
	UserID        string    `json:"user_id" gorm:"not null"`
	CategoryIDs   []int     `json:"category_ids" gorm:"-"`   // For multiple categories
	CategoryNames []string  `json:"category_names" gorm:"-"` // Category names for display
	ImageURL      *string   `json:"image_url,omitempty"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
=======
type Post struct {
<<<<<<< HEAD
    ID       int    `json:"id"`
    Title    string `json:"title"`
    Content  string `json:"content"`
    UserID   int    `json:"user_id"`
    CategoryID int    `json:"category_id"`
}
>>>>>>> 887cfac (models to backend(fs))
=======
    ID          int    `json:"id" validate:"required"`
    Title       string `json:"title" validate:"required,min=3,max=128"`
    Content     string `json:"content" validate:"required,min=3,max=1024"`
    UserID      int    `json:"user_id" validate:"required"`
    CategoryID  int    `json:"category_id" validate:"required"`
}
>>>>>>> 7da9124 (Update backend/models/post.go)
=======
import "time"

type Post struct {
<<<<<<< HEAD
	ID            int       `json:"id" gorm:"primaryKey"`
	Title         string    `json:"title" validate:"required" gorm:"not null"`
	Content       string    `json:"content" validate:"required" gorm:"not null"`
	UserID        int       `json:"user_id" gorm:"not null"`
	CategoryID    *int      `json:"category_id,omitempty"` // Supports both single & multiple categories
	CategoryIDs   []int     `json:"category_ids" gorm:"-"` // Excluded from DB, handled manually
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`
	LikeCount     int       `json:"like_count"`     // Number of likes
	CommentCount   int      `json:"comment_count"`  // Number of comments
}
>>>>>>> 6b85c40 (backend connection)
=======
	ID          int       `json:"id" gorm:"primaryKey"`
	Title       string    `json:"title" validate:"required" gorm:"not null"`
	Content     string    `json:"content" validate:"required" gorm:"not null"`
	Username    string    `json:"username" gorm:"-"`
	UserID      int       `json:"user_id" gorm:"not null"`
	CategoryID  *int      `json:"category_id,omitempty"` // Supports both single & multiple categories
	CategoryIDs []int     `json:"category_ids" gorm:"-"` // Excluded from DB, handled manually
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
>>>>>>> 27c0ab7 (add:username to post struct)
