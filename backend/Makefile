.PHONY: build run clean docker-up docker-down

# Build locally
build:
	go build -o forum-server main.go

# Run locally
run: build
	./forum-server

# Clean up binaries
clean:
	rm -f forum-server

# Docker Commands
docker-up:
<<<<<<< HEAD
<<<<<<< HEAD
	docker-compose up --build
=======
	docker-compose up --build -d
>>>>>>> 6b85c40 (backend connection)
=======
	docker-compose up --build
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)

docker-down:
	docker-compose down
