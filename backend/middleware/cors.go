package middleware

import (
	"net/http"
<<<<<<< HEAD
<<<<<<< HEAD
	"os"
)

// CORS Middleware
func CORS(next http.Handler) http.Handler {
	allowedOrigin := os.Getenv("FRONTEND_ORIGIN")
	if allowedOrigin == "" {
		allowedOrigin = "http://localhost:8000" // fallback default
<<<<<<< HEAD
	}

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// For Docker deployment, requests come through nginx proxy
		// Check if request is from internal Docker network
		origin := r.Header.Get("Origin")
		if origin == "" {
			// No origin header means same-origin request (likely from nginx proxy)
			w.Header().Set("Access-Control-Allow-Origin", allowedOrigin)
		} else if origin == allowedOrigin {
			w.Header().Set("Access-Control-Allow-Origin", origin)
		} else {
			// For development, allow localhost variations
			if origin == "http://localhost:8000" || origin == "http://127.0.0.1:8000" {
				w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", origin)
			} else {
				w.Header().Set("Access-Control-Allow-Origin", allowedOrigin)
			}
		}

		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

=======
=======
	"os"
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
)

// CORS Middleware
func CORS(next http.Handler) http.Handler {
	allowedOrigin := os.Getenv("FRONTEND_ORIGIN")
	if allowedOrigin == "" {
		allowedOrigin = "http://127.0.0.1:8000" // fallback default
=======
>>>>>>> 7d600b4 (chore: add log to log out errors for troubleshooting the code)
	}

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", allowedOrigin)
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

<<<<<<< HEAD
<<<<<<< HEAD
		// Handle preflight (OPTIONS) request
>>>>>>> 6b85c40 (backend connection)
=======
		// Handle preflight OPTIONS request
>>>>>>> efdabf4 (Modified cors to allow multiple origins for local development)
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
