package middleware

import (
	"context"
	"database/sql"
	"net/http"

	"forum/utils"
)

<<<<<<< HEAD
<<<<<<< HEAD
=======
// Define a custom type to avoid key collisions
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> efdabf4 (Modified cors to allow multiple origins for local development)
type contextKey string

const userIDKey contextKey = "userID"

// AuthMiddleware checks if a user is logged in
func AuthMiddleware(db *sql.DB, next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		userID, err := utils.GetUserIDFromSession(db, r)
		if err != nil || userID == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

<<<<<<< HEAD
<<<<<<< HEAD
=======
		// Use the custom key type
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> efdabf4 (Modified cors to allow multiple origins for local development)
		ctx := context.WithValue(r.Context(), userIDKey, userID)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> efdabf4 (Modified cors to allow multiple origins for local development)

// GetUserID extracts userID from request context
func GetUserID(r *http.Request) (string, bool) {
	userID, ok := r.Context().Value(userIDKey).(string)
	return userID, ok
}
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> efdabf4 (Modified cors to allow multiple origins for local development)
