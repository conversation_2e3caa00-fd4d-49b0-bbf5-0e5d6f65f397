# --- Stage 1: Build Stage ---
    FROM golang:1.23.0-alpine AS builder

    # Install required build tools for CGO
    RUN apk add --no-cache gcc musl-dev sqlite-dev

    # Set working directory inside container
    WORKDIR /app
    
    # Copy Go modules and dependencies
    COPY go.mod go.sum ./
    RUN go mod download
    
    # Copy application source code
    COPY . .

    # Ensure static assets exist in builder stage
    RUN ls -la static/pictures/ || echo "Static directory not found"
    
    # Set CGO enabled for sqlite
    ENV CGO_ENABLED=1

    # Build the Go binary
    RUN go build -o forum-server main.go

    # --- Stage 2: Final Minimal Image ---
    FROM alpine:latest
    
    # Install required dependencies (sqlite for database support)
    RUN apk --no-cache add sqlite
    
<<<<<<< HEAD
<<<<<<< HEAD
<<<<<<< HEAD
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
    # Install dependencies for serving static files (e.g., to create necessary directories)
    RUN mkdir -p /app/static && \
        chmod -R 777 /app/static
    
<<<<<<< HEAD
=======
>>>>>>> 6b85c40 (backend connection)
=======
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
=======
    # Create static directories with proper permissions
    RUN mkdir -p /app/static/pictures /app/static/profiles
    RUN chmod -R 755 /app/static

>>>>>>> 3840784 (Major Docker containerization improvements)
    # Set working directory inside container
    WORKDIR /app

    # Copy the built binary from the builder stage
    COPY --from=builder /app/forum-server /app/
<<<<<<< HEAD
    
<<<<<<< HEAD
<<<<<<< HEAD
=======
    COPY --from=builder /app/schema.sql /app/
<<<<<<< HEAD
>>>>>>> 9de5c99 (update post id in comment for filter, update dockerfile to enable cgo gcc)
    # Copy static files if you have any pre-existing static content in the project
    # COPY ./static /app/static
    
=======
>>>>>>> 6b85c40 (backend connection)
=======
    # Copy static files if you have any pre-existing static content in the project
    # COPY ./static /app/static
    
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
=======

    # Copy essential static assets (forum logo and default avatar)
    COPY --from=builder /app/static/pictures/forum-logo.png /app/static/pictures/
    COPY --from=builder /app/static/pictures/icons8-avatar.gif /app/static/pictures/
    COPY --from=builder /app/static/profiles/default.png /app/static/profiles/

    # Ensure proper permissions for static files
    RUN chmod -R 755 /app/static

    # Create data directory for database
    RUN mkdir -p /app/data && chmod -R 755 /app/data

>>>>>>> 3840784 (Major Docker containerization improvements)
    # Expose port (match the port used in `main.go`)
    EXPOSE 8080

    # Start the server
    CMD ["/app/forum-server"]
<<<<<<< HEAD
<<<<<<< HEAD
     
=======
    
>>>>>>> 6b85c40 (backend connection)
=======
     
>>>>>>> ae144ab (chore: transact dummy data to the likes table on post_id raw for likes functionality development.)
